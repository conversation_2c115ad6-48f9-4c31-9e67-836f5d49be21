# bfmap

bfmap is a map tile server,it provides map tile by adpter to other map provoders, like google map, tianditu map, osm map.
it also has map cache for fast access and decrease api request.

## operation and flow

### 系统使用

系统安装后，进入 Setup 页面设置默认用户、初始单位。默认用户可以创建默认的地图提供商 Token，这个 Token 供所有没有配置自身地图提供商 Token 的用户使用。

Setup 完成之后，使用默认用户登录系统，创建其他需要的用户和单位,创建用户时会自动创建一个名为 Default 的关联 Project，用户可以在设置页面配置自己的地图提供商 Token,允许用户为同一个提供商配置多个 Token。

用户可以创建多个单位，只能查看自身所属单位和创建单位的用户，可以修改自身创建单位的用户。

用户可以创建或多个 Project，每个 Project 都可以配置地图 API 的配额。Project 下可以有多个地图 Token，这些地图 Token 共享 Project 的配额，使用这些 Token 可以向系统请求地图数据。更新 Token 值时会先设置旧的 Token 状态为 Deleted,再添加一个新的 Token。

## 地图 API 使用方法

### 获取地图瓦片(Map Tile)

**描述:**

该接口用于获取指定坐标、缩放级别、地图类型和语言的地图瓦片图像。

**请求 URL:** `https://<host>:<port>/map`

**请求方法:** `GET`

**请求参数:**

| 参数名称     | 位置  | 类型    | 是否必选 | 描述                                                                                                                                                                                                             |
| ------------ | ----- | ------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `token`      | Query | String  | 是       | 用户身份验证令牌，用于授权访问 API。                                                                                                                                                                             |
| `x`          | Query | Integer | 是       | 瓦片 X 轴坐标。                                                                                                                                                                                                  |
| `y`          | Query | Integer | 是       | 瓦片 Y 轴坐标。                                                                                                                                                                                                  |
| `z`          | Query | Integer | 是       | 瓦片缩放级别 (Zoom level)。                                                                                                                                                                                      |
| `mtype`      | Query | String  | 可选     | 地图类型 (Map Type)。支持`roadmap`、`satellite`和`hybrid`。 默认为 `roadmap`。                                                                                                                                   |
| `lang`       | Query | String  | 可选     | 语言 (Language)。用于地图元素文本的语言，查看[google 地图支持的语言](https://developers.google.com/maps/faq?hl=zh-cn#languagesupport)。 默认为地图提供商的默认语言，google 为 en，osm 和 tianditu 不可选择语言。 |
| `provider`   | Query | String  | 可选     | 地图服务提供商 (Map Provider): `google`、`tianditu`和`osm(只支持roadmap)`等。 默认为与 token 关联的 provider 。                                                                                                  |
| `gcj02`      | Query | Integer | 可选     | 0/1,是否启用转换地图 wgs84 坐标为 gcj02 坐标，默认为 0，不转换。**只对 google 地图且是国内坐标有效**                                                                                                             |
| `sysname`    | Query | String  | 是       | 系统名称，用于验证系统是否允许使用当前 token。                                                                                                                                                                   |
| `sysexpired` | Query | String  | 可选     | 系统过期标记，如果为 1，表示系统过期，只会返回 bfmap 已缓存的瓦片和本地目录瓦片，不会向在线地图提供商请求。默认为 0。                                                                                            |

**响应:**

- 成功 (200 OK):
  - Content-Type: `image/png`, `image/jpeg`
  - Body: 包含地图瓦片的二进制图像数据, 返回图片大小为 256x256。
- 错误 (400~600):
  - Body: 错误原因，字符串。

**注意事项:**

- 将 `<host>` 和 `<port>` 替换为实际的 API 服务器地址和端口。
- `token` 参数需要替换为用户实际的 API 令牌。
- `mtype`, `lang`，`provider`参数是可选的，如果省略，服务器会使用默认值。
- 错误响应的 JSON 结构和具体错误信息可能会根据实际 API 的实现而有所不同。
- `tianditu`的`lang`只有中文，`osm`为对应位置的语言，如中国是中文，美国是英文。

**示例:**

```url
https://localhost:2243/map?token=xxxxxxx&x=123&y=456&z=7&mtype=roadmap&lang=zh-CN&provider=google&sysname=xxxxx
```

## 本地目录使用说明

### 概述

bfmap 支持本地目录作为地图瓦片的 fallback 机制。当数据库缓存中没有所需瓦片时，系统会自动尝试从本地目录获取瓦片，然后再 fallback 到在线地图服务。这种机制可以提高响应速度，减少网络请求，并在网络不可用时提供离线地图服务。

### 支持的本地目录 Provider 类型

系统支持以下三种本地目录 Provider 类型：

- **ProviderGoogleLocalDirectory (3)** - Google 地图本地目录
- **ProviderTiandituLocalDirectory (4)** - 天地图本地目录
- **ProviderOSMLocalDirectory (5)** - OpenStreetMap 本地目录

### 目录结构要求

本地瓦片文件必须按照以下目录结构存储，坐标体系为 WGS84：

```
{BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}
```

**参数说明：**

- `BaseUrl`: 在地图提供商 Token 配置中设置的本地目录根路径
- `mapType`: 地图类型，支持 `roadmap`、`satellite`、`hybrid`
- `z`: 缩放级别
- `x`, `y`: 瓦片坐标
- `ext`: 文件扩展名，`roadmap`使用`.png`，`satellite`和`hybrid`使用`.jpg`

**示例目录结构：**

```
/path/to/tiles/
├── roadmap/
│   ├── 10/
│   │   ├── 123/
│   │   │   └── 456.png
│   │   └── 124/
│   │       └── 457.png
│   └── 11/
├── satellite/
│   ├── 10/
│   │   ├── 123/
│   │   │   └── 456.jpg
│   │   └── 124/
│   │       └── 457.jpg
└── hybrid/
    └── 10/
        └── 123/
            └── 456.jpg
```

### 配置方法

#### 1. 在 Web 界面配置

1. 登录 bfmap 管理界面
2. 进入"地图提供商 Token"页面
3. 点击"添加"按钮创建新的 Token
4. 在"Provider"下拉菜单中选择对应的本地目录类型：
   - `ProviderGoogleLocalDirectory`
   - `ProviderTiandituLocalDirectory`
   - `ProviderOSMLocalDirectory`
5. 在"BaseUrl"字段中输入本地目录的绝对路径，例如：`/path/to/tiles`
6. 根据需要配置语言字段（仅 Google 本地目录支持多语言）
7. 保存配置

#### 2. BaseUrl 配置说明

- **必须是绝对路径**：如 `/home/<USER>/tiles` 或 `/var/lib/bfmap/tiles`
- **不要包含末尾斜杠**：系统会自动处理路径分隔符
- **确保目录权限**：bfmap 进程需要有读取该目录的权限

### 语言支持

不同的地图提供商对语言的支持程度不同：

#### Google 本地目录 (ProviderGoogleLocalDirectory)

- **支持多语言**：可以为不同语言配置不同的 Token
- **语言匹配**：系统会根据请求的`lang`参数匹配对应语言的 Token
- **配置方法**：在 Token 的"Language"字段中设置语言代码，如`zh-CN`、`en-US`等
- **卫星图跳过语言检查**：satellite 和 hybrid 类型的瓦片不进行语言匹配

#### 天地图本地目录 (ProviderTiandituLocalDirectory)

- **仅支持中文**：只支持`zh-CN`语言
- **自动匹配**：中文请求会自动使用天地图本地目录
- **非中文请求**：会跳过天地图本地目录

#### OSM 本地目录 (ProviderOSMLocalDirectory)

- **跳过语言检查**：OSM 地图的语言由地理位置决定
- **通用使用**：所有语言请求都可以使用同一套 OSM 瓦片

### 工作流程

当收到地图瓦片请求时，系统按以下顺序处理：

1. **数据库缓存查询** - 检查 bbolt 数据库中是否有缓存的瓦片
2. **本地目录 Fallback** - 如果缓存未命中或需要更新，尝试从本地目录获取
   - 根据在线 Provider 类型自动映射到对应的本地目录 Provider 类型
   - 进行语言匹配（如果适用）
   - 查找并读取本地文件
3. **NATS 请求** - 如果本地目录也没有，向 NATS 发送请求（如果启用）
4. **在线 Provider 请求** - 最后 fallback 到在线地图服务

### 调试和监控

#### 启用调试日志

在启动 bfmap 时添加`-debug -debugMap`参数可以看到详细的本地目录访问日志：

```bash
./bfmap -debug -debugMap
```

### 注意事项

1. **文件权限**：确保 bfmap 进程有读取本地目录的权限
2. **磁盘空间**：本地瓦片文件可能占用大量磁盘空间，请合理规划存储
3. **文件完整性**：确保瓦片文件完整且未损坏
4. **路径格式**：严格按照要求的目录结构存储瓦片文件
5. **语言配置**：Google 本地目录需要正确配置 Language 字段以实现语言匹配
6. **优先级**：可以通过 Priority 字段控制多个 Token 的使用优先级

## 开发帮助

### 前后端交互

后端启动时需要添加 `-debug`参数，否则无法跨域访问。

使用`connectrpc`进行前后端交互。有两个 rpc 服务，一个是`bfmap`，另一个是`ProtoDbSrv`。bfmap 服务参考`bfmap.proto`，ProtoDbSrv 服务参考`protodb.proto`。ProtoDbSrv 服务是一个数据库服务，提供了对数据库的基本操作，每个表都有对应的操作权限，具体权限在[dbrpc](rpc/dbrpc.go#L303)的`CreateDbRpcHandler`函数中定义, ProtoDbSrv 服务需要`Session-Id`才能访问。

### ssl 证书

使用 mkcert 生成自签名证书。mkcert 是一个简单的工具，可以为本地开发生成自签名的 SSL 证书。它会自动创建和安装根证书，并为指定的域名生成证书。

```bash
yay -S mkcert
# 安装 mkcert 根证书到系统信任存储
# 这一步只需要执行一次，之后生成的证书会自动信任
mkcert -install
# 生成证书
mkcert -cert-file localhost.crt -key-file localhost.key localhost 127.0.0.1 ::1
```

mkcert 生成的证书可以直接使用，不需要时删除即可。`mkcert -install`安装的根证书可以使用`mkcert -uninstall`卸载。

### 后端调试日志开启方法

- `debug` 调试日志总开关标志
- `debugRpc` rpc 请求调试日志开关标志，开启后，所有 rpc 请求和响应都会打印出来
- `debugMap` 地图 http 请求调试日志开关，开启后，/map 的请求和响应都会打印出来

示例：如果需要开启 rpc 调试日志，设置`-debug -debugRpc`。

### nats 地图瓦片请求

收到地图图片请求且 bbolt 没有数据，根据`setting.ini`配置，向 NATS 发送请求，超时后向地图提供商发起请求。
主题是`map.req`, 消息是 [MapReq](maps/mapreq.go#L12)，消息格式是 json。
订阅主题，收到消息后调用[QueryTileFromKeyValueDb](maps/dbcache.go#L34)查找，回复消息是地图瓦片数据。
