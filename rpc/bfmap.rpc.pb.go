// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: bfmap.rpc.proto

package rpc

import (
	dbproto "bfmap/dbproto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// some response code in common means
type CommonRespCode int32

const (
	CommonRespCode_Success          CommonRespCode = 0
	CommonRespCode_InvalidParam     CommonRespCode = 1
	CommonRespCode_InvalidSessionId CommonRespCode = 2
	CommonRespCode_UnmarshalError   CommonRespCode = 3
	CommonRespCode_DataBaseError    CommonRespCode = 4
	CommonRespCode_ServerError      CommonRespCode = 5
	CommonRespCode_PermissionDenied CommonRespCode = 6
	CommonRespCode_NotFound         CommonRespCode = 7
	CommonRespCode_AlreadyExist     CommonRespCode = 8
	CommonRespCode_Unknown          CommonRespCode = 9
)

// Enum value maps for CommonRespCode.
var (
	CommonRespCode_name = map[int32]string{
		0: "Success",
		1: "InvalidParam",
		2: "InvalidSessionId",
		3: "UnmarshalError",
		4: "DataBaseError",
		5: "ServerError",
		6: "PermissionDenied",
		7: "NotFound",
		8: "AlreadyExist",
		9: "Unknown",
	}
	CommonRespCode_value = map[string]int32{
		"Success":          0,
		"InvalidParam":     1,
		"InvalidSessionId": 2,
		"UnmarshalError":   3,
		"DataBaseError":    4,
		"ServerError":      5,
		"PermissionDenied": 6,
		"NotFound":         7,
		"AlreadyExist":     8,
		"Unknown":          9,
	}
)

func (x CommonRespCode) Enum() *CommonRespCode {
	p := new(CommonRespCode)
	*p = x
	return p
}

func (x CommonRespCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommonRespCode) Descriptor() protoreflect.EnumDescriptor {
	return file_bfmap_rpc_proto_enumTypes[0].Descriptor()
}

func (CommonRespCode) Type() protoreflect.EnumType {
	return &file_bfmap_rpc_proto_enumTypes[0]
}

func (x CommonRespCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommonRespCode.Descriptor instead.
func (CommonRespCode) EnumDescriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{0}
}

type LoginMethod int32

const (
	LoginMethod_Password  LoginMethod = 0
	LoginMethod_SessionId LoginMethod = 1
)

// Enum value maps for LoginMethod.
var (
	LoginMethod_name = map[int32]string{
		0: "Password",
		1: "SessionId",
	}
	LoginMethod_value = map[string]int32{
		"Password":  0,
		"SessionId": 1,
	}
)

func (x LoginMethod) Enum() *LoginMethod {
	p := new(LoginMethod)
	*p = x
	return p
}

func (x LoginMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoginMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_bfmap_rpc_proto_enumTypes[1].Descriptor()
}

func (LoginMethod) Type() protoreflect.EnumType {
	return &file_bfmap_rpc_proto_enumTypes[1]
}

func (x LoginMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoginMethod.Descriptor instead.
func (LoginMethod) EnumDescriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{1}
}

type LoginRespCode int32

const (
	LoginRespCode_LoginSuccess      LoginRespCode = 0
	LoginRespCode_InvalidLoginParam LoginRespCode = 1
	// the time in LoginReq is too old, it should be less than 5 minutes from now
	LoginRespCode_ReqTimeTooOld LoginRespCode = 2
	// the time in LoginReq is too new, it should not be more than 1 minute from now
	LoginRespCode_ReqTimeTooNew         LoginRespCode = 3
	LoginRespCode_UserNotExist          LoginRespCode = 4
	LoginRespCode_PasswordNotMatch      LoginRespCode = 5
	LoginRespCode_SessionIdNotExist     LoginRespCode = 6
	LoginRespCode_SessionIdExpired      LoginRespCode = 7
	LoginRespCode_SessionAlreadyLogin   LoginRespCode = 8
	LoginRespCode_FailWithInternalError LoginRespCode = 9
	LoginRespCode_UserDisabled          LoginRespCode = 10
)

// Enum value maps for LoginRespCode.
var (
	LoginRespCode_name = map[int32]string{
		0:  "LoginSuccess",
		1:  "InvalidLoginParam",
		2:  "ReqTimeTooOld",
		3:  "ReqTimeTooNew",
		4:  "UserNotExist",
		5:  "PasswordNotMatch",
		6:  "SessionIdNotExist",
		7:  "SessionIdExpired",
		8:  "SessionAlreadyLogin",
		9:  "FailWithInternalError",
		10: "UserDisabled",
	}
	LoginRespCode_value = map[string]int32{
		"LoginSuccess":          0,
		"InvalidLoginParam":     1,
		"ReqTimeTooOld":         2,
		"ReqTimeTooNew":         3,
		"UserNotExist":          4,
		"PasswordNotMatch":      5,
		"SessionIdNotExist":     6,
		"SessionIdExpired":      7,
		"SessionAlreadyLogin":   8,
		"FailWithInternalError": 9,
		"UserDisabled":          10,
	}
)

func (x LoginRespCode) Enum() *LoginRespCode {
	p := new(LoginRespCode)
	*p = x
	return p
}

func (x LoginRespCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoginRespCode) Descriptor() protoreflect.EnumDescriptor {
	return file_bfmap_rpc_proto_enumTypes[2].Descriptor()
}

func (LoginRespCode) Type() protoreflect.EnumType {
	return &file_bfmap_rpc_proto_enumTypes[2]
}

func (x LoginRespCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoginRespCode.Descriptor instead.
func (LoginRespCode) EnumDescriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{2}
}

// empty msg
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_bfmap_rpc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{0}
}

// common msg
type Common struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	Reason        string                 `protobuf:"bytes,2,opt,name=Reason,proto3" json:"Reason,omitempty"`
	Msg           string                 `protobuf:"bytes,3,opt,name=Msg,proto3" json:"Msg,omitempty"`
	Body          []byte                 `protobuf:"bytes,4,opt,name=Body,proto3" json:"Body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Common) Reset() {
	*x = Common{}
	mi := &file_bfmap_rpc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Common) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Common) ProtoMessage() {}

func (x *Common) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Common.ProtoReflect.Descriptor instead.
func (*Common) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{1}
}

func (x *Common) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Common) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *Common) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *Common) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

type SetupReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Name  string                 `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`
	// password, base64(sha256(Name+Password))
	Password      string `protobuf:"bytes,2,opt,name=Password,proto3" json:"Password,omitempty"`
	OrgName       string `protobuf:"bytes,3,opt,name=OrgName,proto3" json:"OrgName,omitempty"`
	ProjectName   string `protobuf:"bytes,4,opt,name=ProjectName,proto3" json:"ProjectName,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetupReq) Reset() {
	*x = SetupReq{}
	mi := &file_bfmap_rpc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetupReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetupReq) ProtoMessage() {}

func (x *SetupReq) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetupReq.ProtoReflect.Descriptor instead.
func (*SetupReq) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{2}
}

func (x *SetupReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SetupReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SetupReq) GetOrgName() string {
	if x != nil {
		return x.OrgName
	}
	return ""
}

func (x *SetupReq) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

type LoginReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Name  string                 `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`
	// 0: password login，1: session id login
	LoginMethod LoginMethod `protobuf:"varint,2,opt,name=LoginMethod,proto3,enum=rpc.LoginMethod" json:"LoginMethod,omitempty"`
	// value is base64(sha256(time_str+base64(sha256(Name+Password))))
	PasswordHash string `protobuf:"bytes,3,opt,name=PasswordHash,proto3" json:"PasswordHash,omitempty"`
	// format in utc time: yyyy-mm-dd hh:mm:ss
	TimeStr       string `protobuf:"bytes,4,opt,name=TimeStr,proto3" json:"TimeStr,omitempty"`
	SessionId     string `protobuf:"bytes,5,opt,name=SessionId,proto3" json:"SessionId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginReq) Reset() {
	*x = LoginReq{}
	mi := &file_bfmap_rpc_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReq) ProtoMessage() {}

func (x *LoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReq.ProtoReflect.Descriptor instead.
func (*LoginReq) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{3}
}

func (x *LoginReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LoginReq) GetLoginMethod() LoginMethod {
	if x != nil {
		return x.LoginMethod
	}
	return LoginMethod_Password
}

func (x *LoginReq) GetPasswordHash() string {
	if x != nil {
		return x.PasswordHash
	}
	return ""
}

func (x *LoginReq) GetTimeStr() string {
	if x != nil {
		return x.TimeStr
	}
	return ""
}

func (x *LoginReq) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type LoginResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Code  LoginRespCode          `protobuf:"varint,1,opt,name=Code,proto3,enum=rpc.LoginRespCode" json:"Code,omitempty"`
	// if login fail, the reason will be set
	Reason        string `protobuf:"bytes,2,opt,name=Reason,proto3" json:"Reason,omitempty"`
	SessionId     string `protobuf:"bytes,3,opt,name=SessionId,proto3" json:"SessionId,omitempty"`
	UserRid       string `protobuf:"bytes,4,opt,name=UserRid,proto3" json:"UserRid,omitempty"`
	UserOrgRid    string `protobuf:"bytes,5,opt,name=UserOrgRid,proto3" json:"UserOrgRid,omitempty"`
	ServerVersion string `protobuf:"bytes,6,opt,name=ServerVersion,proto3" json:"ServerVersion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginResp) Reset() {
	*x = LoginResp{}
	mi := &file_bfmap_rpc_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResp) ProtoMessage() {}

func (x *LoginResp) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResp.ProtoReflect.Descriptor instead.
func (*LoginResp) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{4}
}

func (x *LoginResp) GetCode() LoginRespCode {
	if x != nil {
		return x.Code
	}
	return LoginRespCode_LoginSuccess
}

func (x *LoginResp) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *LoginResp) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *LoginResp) GetUserRid() string {
	if x != nil {
		return x.UserRid
	}
	return ""
}

func (x *LoginResp) GetUserOrgRid() string {
	if x != nil {
		return x.UserOrgRid
	}
	return ""
}

func (x *LoginResp) GetServerVersion() string {
	if x != nil {
		return x.ServerVersion
	}
	return ""
}

type CreateUserReq struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	User          *dbproto.DbUser          `protobuf:"bytes,1,opt,name=User,proto3" json:"User,omitempty"`
	UserPrivilege *dbproto.DbUserPrivilege `protobuf:"bytes,2,opt,name=UserPrivilege,proto3" json:"UserPrivilege,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserReq) Reset() {
	*x = CreateUserReq{}
	mi := &file_bfmap_rpc_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserReq) ProtoMessage() {}

func (x *CreateUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserReq.ProtoReflect.Descriptor instead.
func (*CreateUserReq) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{5}
}

func (x *CreateUserReq) GetUser() *dbproto.DbUser {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *CreateUserReq) GetUserPrivilege() *dbproto.DbUserPrivilege {
	if x != nil {
		return x.UserPrivilege
	}
	return nil
}

type SetProviderTokenReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 1:create
	// 2:update,if UpdateFields is empty, all the fields will be updated
	// 4:delete, mark old one as "delete"
	// 8:force delete,delete in db
	// Common.Body is DbMapProviderToken
	Code          int32                       `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	ProviderToken *dbproto.DbMapProviderToken `protobuf:"bytes,2,opt,name=ProviderToken,proto3" json:"ProviderToken,omitempty"`
	UpdateFields  []string                    `protobuf:"bytes,3,rep,name=UpdateFields,proto3" json:"UpdateFields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetProviderTokenReq) Reset() {
	*x = SetProviderTokenReq{}
	mi := &file_bfmap_rpc_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetProviderTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetProviderTokenReq) ProtoMessage() {}

func (x *SetProviderTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetProviderTokenReq.ProtoReflect.Descriptor instead.
func (*SetProviderTokenReq) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{6}
}

func (x *SetProviderTokenReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SetProviderTokenReq) GetProviderToken() *dbproto.DbMapProviderToken {
	if x != nil {
		return x.ProviderToken
	}
	return nil
}

func (x *SetProviderTokenReq) GetUpdateFields() []string {
	if x != nil {
		return x.UpdateFields
	}
	return nil
}

type SetProjectReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 1:create
	// 2:update,if UpdateFields is empty, all the fields will be updated,
	// 4:delete, mark old one as "delete"
	// 8:force delete,delete in db
	// Common.Body is DbMapProviderToken
	Code int32 `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	// set empty if do not want to update this
	DbProject *dbproto.DbProject `protobuf:"bytes,2,opt,name=DbProject,proto3" json:"DbProject,omitempty"`
	// set empty if do not want to update this
	DbProjectQuotas     *dbproto.DbProjectQuotas `protobuf:"bytes,3,opt,name=DbProjectQuotas,proto3" json:"DbProjectQuotas,omitempty"`
	UpdateProjectFields []string                 `protobuf:"bytes,4,rep,name=UpdateProjectFields,proto3" json:"UpdateProjectFields,omitempty"`
	UpdateQuotasFields  []string                 `protobuf:"bytes,5,rep,name=UpdateQuotasFields,proto3" json:"UpdateQuotasFields,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *SetProjectReq) Reset() {
	*x = SetProjectReq{}
	mi := &file_bfmap_rpc_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetProjectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetProjectReq) ProtoMessage() {}

func (x *SetProjectReq) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetProjectReq.ProtoReflect.Descriptor instead.
func (*SetProjectReq) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{7}
}

func (x *SetProjectReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SetProjectReq) GetDbProject() *dbproto.DbProject {
	if x != nil {
		return x.DbProject
	}
	return nil
}

func (x *SetProjectReq) GetDbProjectQuotas() *dbproto.DbProjectQuotas {
	if x != nil {
		return x.DbProjectQuotas
	}
	return nil
}

func (x *SetProjectReq) GetUpdateProjectFields() []string {
	if x != nil {
		return x.UpdateProjectFields
	}
	return nil
}

func (x *SetProjectReq) GetUpdateQuotasFields() []string {
	if x != nil {
		return x.UpdateQuotasFields
	}
	return nil
}

type SetProjectTokenReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 1:create
	// 2:update, if UpdateFields is empty, all the fields will be updated
	// 3:rotate token,create new one and mark old one status as "delete",
	// new DbProjectToken return in Common.Body
	// 4:delete, mark old one as "delete"
	// 8:force delete,delete in db
	// Common.Body is DbMapProviderToken
	Code          int32                   `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	ProjectToken  *dbproto.DbProjectToken `protobuf:"bytes,2,opt,name=ProjectToken,proto3" json:"ProjectToken,omitempty"`
	UpdateFields  []string                `protobuf:"bytes,3,rep,name=UpdateFields,proto3" json:"UpdateFields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetProjectTokenReq) Reset() {
	*x = SetProjectTokenReq{}
	mi := &file_bfmap_rpc_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetProjectTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetProjectTokenReq) ProtoMessage() {}

func (x *SetProjectTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetProjectTokenReq.ProtoReflect.Descriptor instead.
func (*SetProjectTokenReq) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{8}
}

func (x *SetProjectTokenReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SetProjectTokenReq) GetProjectToken() *dbproto.DbProjectToken {
	if x != nil {
		return x.ProjectToken
	}
	return nil
}

func (x *SetProjectTokenReq) GetUpdateFields() []string {
	if x != nil {
		return x.UpdateFields
	}
	return nil
}

type DeleteMapCacheIndexesReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// map type: 1:roadmap 2:satellite 3: hybrid
	MapType   int32                     `protobuf:"varint,1,opt,name=MapType,proto3" json:"MapType,omitempty"`
	Providers []dbproto.MapProviderEnum `protobuf:"varint,2,rep,packed,name=Providers,proto3,enum=dbproto.MapProviderEnum" json:"Providers,omitempty"`
	// max zoom is 22
	Zoom   int32   `protobuf:"varint,3,opt,name=Zoom,proto3" json:"Zoom,omitempty"`
	MinLon float64 `protobuf:"fixed64,4,opt,name=MinLon,proto3" json:"MinLon,omitempty"`
	MaxLon float64 `protobuf:"fixed64,5,opt,name=MaxLon,proto3" json:"MaxLon,omitempty"`
	MinLat float64 `protobuf:"fixed64,6,opt,name=MinLat,proto3" json:"MinLat,omitempty"`
	MaxLat float64 `protobuf:"fixed64,7,opt,name=MaxLat,proto3" json:"MaxLat,omitempty"`
	// <=0: no limit
	CacheTime     int64 `protobuf:"varint,8,opt,name=CacheTime,proto3" json:"CacheTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMapCacheIndexesReq) Reset() {
	*x = DeleteMapCacheIndexesReq{}
	mi := &file_bfmap_rpc_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMapCacheIndexesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMapCacheIndexesReq) ProtoMessage() {}

func (x *DeleteMapCacheIndexesReq) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMapCacheIndexesReq.ProtoReflect.Descriptor instead.
func (*DeleteMapCacheIndexesReq) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteMapCacheIndexesReq) GetMapType() int32 {
	if x != nil {
		return x.MapType
	}
	return 0
}

func (x *DeleteMapCacheIndexesReq) GetProviders() []dbproto.MapProviderEnum {
	if x != nil {
		return x.Providers
	}
	return nil
}

func (x *DeleteMapCacheIndexesReq) GetZoom() int32 {
	if x != nil {
		return x.Zoom
	}
	return 0
}

func (x *DeleteMapCacheIndexesReq) GetMinLon() float64 {
	if x != nil {
		return x.MinLon
	}
	return 0
}

func (x *DeleteMapCacheIndexesReq) GetMaxLon() float64 {
	if x != nil {
		return x.MaxLon
	}
	return 0
}

func (x *DeleteMapCacheIndexesReq) GetMinLat() float64 {
	if x != nil {
		return x.MinLat
	}
	return 0
}

func (x *DeleteMapCacheIndexesReq) GetMaxLat() float64 {
	if x != nil {
		return x.MaxLat
	}
	return 0
}

func (x *DeleteMapCacheIndexesReq) GetCacheTime() int64 {
	if x != nil {
		return x.CacheTime
	}
	return 0
}

type GetCurrentUsageReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 1:provider token 2:project 3:project token
	Code int32 `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	// for provider token
	UserRid string `protobuf:"bytes,2,opt,name=UserRid,proto3" json:"UserRid,omitempty"`
	// for provider token
	Provider dbproto.MapProviderEnum `protobuf:"varint,3,opt,name=Provider,proto3,enum=dbproto.MapProviderEnum" json:"Provider,omitempty"`
	// for provider token
	ProviderToken string `protobuf:"bytes,4,opt,name=ProviderToken,proto3" json:"ProviderToken,omitempty"`
	// for project
	ProjectRid string `protobuf:"bytes,5,opt,name=ProjectRid,proto3" json:"ProjectRid,omitempty"`
	// for project token
	ProjectToken  string `protobuf:"bytes,6,opt,name=ProjectToken,proto3" json:"ProjectToken,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCurrentUsageReq) Reset() {
	*x = GetCurrentUsageReq{}
	mi := &file_bfmap_rpc_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCurrentUsageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentUsageReq) ProtoMessage() {}

func (x *GetCurrentUsageReq) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentUsageReq.ProtoReflect.Descriptor instead.
func (*GetCurrentUsageReq) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{10}
}

func (x *GetCurrentUsageReq) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCurrentUsageReq) GetUserRid() string {
	if x != nil {
		return x.UserRid
	}
	return ""
}

func (x *GetCurrentUsageReq) GetProvider() dbproto.MapProviderEnum {
	if x != nil {
		return x.Provider
	}
	return dbproto.MapProviderEnum(0)
}

func (x *GetCurrentUsageReq) GetProviderToken() string {
	if x != nil {
		return x.ProviderToken
	}
	return ""
}

func (x *GetCurrentUsageReq) GetProjectRid() string {
	if x != nil {
		return x.ProjectRid
	}
	return ""
}

func (x *GetCurrentUsageReq) GetProjectToken() string {
	if x != nil {
		return x.ProjectToken
	}
	return ""
}

type GetCurrentUsageResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 0:success, !0:fail
	Code          int32  `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	Reason        string `protobuf:"bytes,2,opt,name=Reason,proto3" json:"Reason,omitempty"`
	CurrentUsage  int32  `protobuf:"varint,3,opt,name=CurrentUsage,proto3" json:"CurrentUsage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCurrentUsageResp) Reset() {
	*x = GetCurrentUsageResp{}
	mi := &file_bfmap_rpc_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCurrentUsageResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentUsageResp) ProtoMessage() {}

func (x *GetCurrentUsageResp) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentUsageResp.ProtoReflect.Descriptor instead.
func (*GetCurrentUsageResp) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{11}
}

func (x *GetCurrentUsageResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetCurrentUsageResp) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *GetCurrentUsageResp) GetCurrentUsage() int32 {
	if x != nil {
		return x.CurrentUsage
	}
	return 0
}

type UpdateDbUserReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	User  *dbproto.DbUser        `protobuf:"bytes,1,opt,name=User,proto3" json:"User,omitempty"`
	// Optional: If provided, only update these fields
	UpdateFields  []string `protobuf:"bytes,2,rep,name=UpdateFields,proto3" json:"UpdateFields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDbUserReq) Reset() {
	*x = UpdateDbUserReq{}
	mi := &file_bfmap_rpc_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDbUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDbUserReq) ProtoMessage() {}

func (x *UpdateDbUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_bfmap_rpc_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDbUserReq.ProtoReflect.Descriptor instead.
func (*UpdateDbUserReq) Descriptor() ([]byte, []int) {
	return file_bfmap_rpc_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateDbUserReq) GetUser() *dbproto.DbUser {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *UpdateDbUserReq) GetUpdateFields() []string {
	if x != nil {
		return x.UpdateFields
	}
	return nil
}

var File_bfmap_rpc_proto protoreflect.FileDescriptor

const file_bfmap_rpc_proto_rawDesc = "" +
	"\n" +
	"\x0fbfmap.rpc.proto\x12\x03rpc\x1a\bdb.proto\"\a\n" +
	"\x05Empty\"Z\n" +
	"\x06Common\x12\x12\n" +
	"\x04Code\x18\x01 \x01(\x05R\x04Code\x12\x16\n" +
	"\x06Reason\x18\x02 \x01(\tR\x06Reason\x12\x10\n" +
	"\x03Msg\x18\x03 \x01(\tR\x03Msg\x12\x12\n" +
	"\x04Body\x18\x04 \x01(\fR\x04Body\"v\n" +
	"\bSetupReq\x12\x12\n" +
	"\x04Name\x18\x01 \x01(\tR\x04Name\x12\x1a\n" +
	"\bPassword\x18\x02 \x01(\tR\bPassword\x12\x18\n" +
	"\aOrgName\x18\x03 \x01(\tR\aOrgName\x12 \n" +
	"\vProjectName\x18\x04 \x01(\tR\vProjectName\"\xae\x01\n" +
	"\bLoginReq\x12\x12\n" +
	"\x04Name\x18\x01 \x01(\tR\x04Name\x122\n" +
	"\vLoginMethod\x18\x02 \x01(\x0e2\x10.rpc.LoginMethodR\vLoginMethod\x12\"\n" +
	"\fPasswordHash\x18\x03 \x01(\tR\fPasswordHash\x12\x18\n" +
	"\aTimeStr\x18\x04 \x01(\tR\aTimeStr\x12\x1c\n" +
	"\tSessionId\x18\x05 \x01(\tR\tSessionId\"\xc9\x01\n" +
	"\tLoginResp\x12&\n" +
	"\x04Code\x18\x01 \x01(\x0e2\x12.rpc.LoginRespCodeR\x04Code\x12\x16\n" +
	"\x06Reason\x18\x02 \x01(\tR\x06Reason\x12\x1c\n" +
	"\tSessionId\x18\x03 \x01(\tR\tSessionId\x12\x18\n" +
	"\aUserRid\x18\x04 \x01(\tR\aUserRid\x12\x1e\n" +
	"\n" +
	"UserOrgRid\x18\x05 \x01(\tR\n" +
	"UserOrgRid\x12$\n" +
	"\rServerVersion\x18\x06 \x01(\tR\rServerVersion\"t\n" +
	"\rCreateUserReq\x12#\n" +
	"\x04User\x18\x01 \x01(\v2\x0f.dbproto.DbUserR\x04User\x12>\n" +
	"\rUserPrivilege\x18\x02 \x01(\v2\x18.dbproto.DbUserPrivilegeR\rUserPrivilege\"\x90\x01\n" +
	"\x13SetProviderTokenReq\x12\x12\n" +
	"\x04Code\x18\x01 \x01(\x05R\x04Code\x12A\n" +
	"\rProviderToken\x18\x02 \x01(\v2\x1b.dbproto.DbMapProviderTokenR\rProviderToken\x12\"\n" +
	"\fUpdateFields\x18\x03 \x03(\tR\fUpdateFields\"\xfb\x01\n" +
	"\rSetProjectReq\x12\x12\n" +
	"\x04Code\x18\x01 \x01(\x05R\x04Code\x120\n" +
	"\tDbProject\x18\x02 \x01(\v2\x12.dbproto.DbProjectR\tDbProject\x12B\n" +
	"\x0fDbProjectQuotas\x18\x03 \x01(\v2\x18.dbproto.DbProjectQuotasR\x0fDbProjectQuotas\x120\n" +
	"\x13UpdateProjectFields\x18\x04 \x03(\tR\x13UpdateProjectFields\x12.\n" +
	"\x12UpdateQuotasFields\x18\x05 \x03(\tR\x12UpdateQuotasFields\"\x89\x01\n" +
	"\x12SetProjectTokenReq\x12\x12\n" +
	"\x04Code\x18\x01 \x01(\x05R\x04Code\x12;\n" +
	"\fProjectToken\x18\x02 \x01(\v2\x17.dbproto.DbProjectTokenR\fProjectToken\x12\"\n" +
	"\fUpdateFields\x18\x03 \x03(\tR\fUpdateFields\"\xfe\x01\n" +
	"\x18DeleteMapCacheIndexesReq\x12\x18\n" +
	"\aMapType\x18\x01 \x01(\x05R\aMapType\x126\n" +
	"\tProviders\x18\x02 \x03(\x0e2\x18.dbproto.MapProviderEnumR\tProviders\x12\x12\n" +
	"\x04Zoom\x18\x03 \x01(\x05R\x04Zoom\x12\x16\n" +
	"\x06MinLon\x18\x04 \x01(\x01R\x06MinLon\x12\x16\n" +
	"\x06MaxLon\x18\x05 \x01(\x01R\x06MaxLon\x12\x16\n" +
	"\x06MinLat\x18\x06 \x01(\x01R\x06MinLat\x12\x16\n" +
	"\x06MaxLat\x18\a \x01(\x01R\x06MaxLat\x12\x1c\n" +
	"\tCacheTime\x18\b \x01(\x03R\tCacheTime\"\xe2\x01\n" +
	"\x12GetCurrentUsageReq\x12\x12\n" +
	"\x04Code\x18\x01 \x01(\x05R\x04Code\x12\x18\n" +
	"\aUserRid\x18\x02 \x01(\tR\aUserRid\x124\n" +
	"\bProvider\x18\x03 \x01(\x0e2\x18.dbproto.MapProviderEnumR\bProvider\x12$\n" +
	"\rProviderToken\x18\x04 \x01(\tR\rProviderToken\x12\x1e\n" +
	"\n" +
	"ProjectRid\x18\x05 \x01(\tR\n" +
	"ProjectRid\x12\"\n" +
	"\fProjectToken\x18\x06 \x01(\tR\fProjectToken\"e\n" +
	"\x13GetCurrentUsageResp\x12\x12\n" +
	"\x04Code\x18\x01 \x01(\x05R\x04Code\x12\x16\n" +
	"\x06Reason\x18\x02 \x01(\tR\x06Reason\x12\"\n" +
	"\fCurrentUsage\x18\x03 \x01(\x05R\fCurrentUsage\"Z\n" +
	"\x0fUpdateDbUserReq\x12#\n" +
	"\x04User\x18\x01 \x01(\v2\x0f.dbproto.DbUserR\x04User\x12\"\n" +
	"\fUpdateFields\x18\x02 \x03(\tR\fUpdateFields*\xc0\x01\n" +
	"\x0eCommonRespCode\x12\v\n" +
	"\aSuccess\x10\x00\x12\x10\n" +
	"\fInvalidParam\x10\x01\x12\x14\n" +
	"\x10InvalidSessionId\x10\x02\x12\x12\n" +
	"\x0eUnmarshalError\x10\x03\x12\x11\n" +
	"\rDataBaseError\x10\x04\x12\x0f\n" +
	"\vServerError\x10\x05\x12\x14\n" +
	"\x10PermissionDenied\x10\x06\x12\f\n" +
	"\bNotFound\x10\a\x12\x10\n" +
	"\fAlreadyExist\x10\b\x12\v\n" +
	"\aUnknown\x10\t**\n" +
	"\vLoginMethod\x12\f\n" +
	"\bPassword\x10\x00\x12\r\n" +
	"\tSessionId\x10\x01*\xf9\x01\n" +
	"\rLoginRespCode\x12\x10\n" +
	"\fLoginSuccess\x10\x00\x12\x15\n" +
	"\x11InvalidLoginParam\x10\x01\x12\x11\n" +
	"\rReqTimeTooOld\x10\x02\x12\x11\n" +
	"\rReqTimeTooNew\x10\x03\x12\x10\n" +
	"\fUserNotExist\x10\x04\x12\x14\n" +
	"\x10PasswordNotMatch\x10\x05\x12\x15\n" +
	"\x11SessionIdNotExist\x10\x06\x12\x14\n" +
	"\x10SessionIdExpired\x10\a\x12\x17\n" +
	"\x13SessionAlreadyLogin\x10\b\x12\x19\n" +
	"\x15FailWithInternalError\x10\t\x12\x10\n" +
	"\fUserDisabled\x10\n" +
	"2\x82\x05\n" +
	"\x05bfmap\x12\"\n" +
	"\aIsSetup\x12\n" +
	".rpc.Empty\x1a\v.rpc.Common\x12#\n" +
	"\x05Setup\x12\r.rpc.SetupReq\x1a\v.rpc.Common\x12&\n" +
	"\x05Login\x12\r.rpc.LoginReq\x1a\x0e.rpc.LoginResp\x12!\n" +
	"\x06Logout\x12\n" +
	".rpc.Empty\x1a\v.rpc.Common\x12\x1f\n" +
	"\x04Ping\x12\n" +
	".rpc.Empty\x1a\v.rpc.Common\x12-\n" +
	"\n" +
	"CreateUser\x12\x12.rpc.CreateUserReq\x1a\v.rpc.Common\x129\n" +
	"\x10SetProviderToken\x12\x18.rpc.SetProviderTokenReq\x1a\v.rpc.Common\x12-\n" +
	"\n" +
	"SetProject\x12\x12.rpc.SetProjectReq\x1a\v.rpc.Common\x127\n" +
	"\x0fSetProjectToken\x12\x17.rpc.SetProjectTokenReq\x1a\v.rpc.Common\x124\n" +
	"\x19CreateTempMapProjectToken\x12\n" +
	".rpc.Empty\x1a\v.rpc.Common\x12C\n" +
	"\x15DeleteMapCacheIndexes\x12\x1d.rpc.DeleteMapCacheIndexesReq\x1a\v.rpc.Common\x12D\n" +
	"\x0fGetCurrentUsage\x12\x17.rpc.GetCurrentUsageReq\x1a\x18.rpc.GetCurrentUsageResp\x121\n" +
	"\fUpdateDbUser\x12\x14.rpc.UpdateDbUserReq\x1a\v.rpc.CommonB\vZ\tbfmap/rpcb\x06proto3"

var (
	file_bfmap_rpc_proto_rawDescOnce sync.Once
	file_bfmap_rpc_proto_rawDescData []byte
)

func file_bfmap_rpc_proto_rawDescGZIP() []byte {
	file_bfmap_rpc_proto_rawDescOnce.Do(func() {
		file_bfmap_rpc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_bfmap_rpc_proto_rawDesc), len(file_bfmap_rpc_proto_rawDesc)))
	})
	return file_bfmap_rpc_proto_rawDescData
}

var file_bfmap_rpc_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_bfmap_rpc_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_bfmap_rpc_proto_goTypes = []any{
	(CommonRespCode)(0),                // 0: rpc.CommonRespCode
	(LoginMethod)(0),                   // 1: rpc.LoginMethod
	(LoginRespCode)(0),                 // 2: rpc.LoginRespCode
	(*Empty)(nil),                      // 3: rpc.Empty
	(*Common)(nil),                     // 4: rpc.Common
	(*SetupReq)(nil),                   // 5: rpc.SetupReq
	(*LoginReq)(nil),                   // 6: rpc.LoginReq
	(*LoginResp)(nil),                  // 7: rpc.LoginResp
	(*CreateUserReq)(nil),              // 8: rpc.CreateUserReq
	(*SetProviderTokenReq)(nil),        // 9: rpc.SetProviderTokenReq
	(*SetProjectReq)(nil),              // 10: rpc.SetProjectReq
	(*SetProjectTokenReq)(nil),         // 11: rpc.SetProjectTokenReq
	(*DeleteMapCacheIndexesReq)(nil),   // 12: rpc.DeleteMapCacheIndexesReq
	(*GetCurrentUsageReq)(nil),         // 13: rpc.GetCurrentUsageReq
	(*GetCurrentUsageResp)(nil),        // 14: rpc.GetCurrentUsageResp
	(*UpdateDbUserReq)(nil),            // 15: rpc.UpdateDbUserReq
	(*dbproto.DbUser)(nil),             // 16: dbproto.DbUser
	(*dbproto.DbUserPrivilege)(nil),    // 17: dbproto.DbUserPrivilege
	(*dbproto.DbMapProviderToken)(nil), // 18: dbproto.DbMapProviderToken
	(*dbproto.DbProject)(nil),          // 19: dbproto.DbProject
	(*dbproto.DbProjectQuotas)(nil),    // 20: dbproto.DbProjectQuotas
	(*dbproto.DbProjectToken)(nil),     // 21: dbproto.DbProjectToken
	(dbproto.MapProviderEnum)(0),       // 22: dbproto.MapProviderEnum
}
var file_bfmap_rpc_proto_depIdxs = []int32{
	1,  // 0: rpc.LoginReq.LoginMethod:type_name -> rpc.LoginMethod
	2,  // 1: rpc.LoginResp.Code:type_name -> rpc.LoginRespCode
	16, // 2: rpc.CreateUserReq.User:type_name -> dbproto.DbUser
	17, // 3: rpc.CreateUserReq.UserPrivilege:type_name -> dbproto.DbUserPrivilege
	18, // 4: rpc.SetProviderTokenReq.ProviderToken:type_name -> dbproto.DbMapProviderToken
	19, // 5: rpc.SetProjectReq.DbProject:type_name -> dbproto.DbProject
	20, // 6: rpc.SetProjectReq.DbProjectQuotas:type_name -> dbproto.DbProjectQuotas
	21, // 7: rpc.SetProjectTokenReq.ProjectToken:type_name -> dbproto.DbProjectToken
	22, // 8: rpc.DeleteMapCacheIndexesReq.Providers:type_name -> dbproto.MapProviderEnum
	22, // 9: rpc.GetCurrentUsageReq.Provider:type_name -> dbproto.MapProviderEnum
	16, // 10: rpc.UpdateDbUserReq.User:type_name -> dbproto.DbUser
	3,  // 11: rpc.bfmap.IsSetup:input_type -> rpc.Empty
	5,  // 12: rpc.bfmap.Setup:input_type -> rpc.SetupReq
	6,  // 13: rpc.bfmap.Login:input_type -> rpc.LoginReq
	3,  // 14: rpc.bfmap.Logout:input_type -> rpc.Empty
	3,  // 15: rpc.bfmap.Ping:input_type -> rpc.Empty
	8,  // 16: rpc.bfmap.CreateUser:input_type -> rpc.CreateUserReq
	9,  // 17: rpc.bfmap.SetProviderToken:input_type -> rpc.SetProviderTokenReq
	10, // 18: rpc.bfmap.SetProject:input_type -> rpc.SetProjectReq
	11, // 19: rpc.bfmap.SetProjectToken:input_type -> rpc.SetProjectTokenReq
	3,  // 20: rpc.bfmap.CreateTempMapProjectToken:input_type -> rpc.Empty
	12, // 21: rpc.bfmap.DeleteMapCacheIndexes:input_type -> rpc.DeleteMapCacheIndexesReq
	13, // 22: rpc.bfmap.GetCurrentUsage:input_type -> rpc.GetCurrentUsageReq
	15, // 23: rpc.bfmap.UpdateDbUser:input_type -> rpc.UpdateDbUserReq
	4,  // 24: rpc.bfmap.IsSetup:output_type -> rpc.Common
	4,  // 25: rpc.bfmap.Setup:output_type -> rpc.Common
	7,  // 26: rpc.bfmap.Login:output_type -> rpc.LoginResp
	4,  // 27: rpc.bfmap.Logout:output_type -> rpc.Common
	4,  // 28: rpc.bfmap.Ping:output_type -> rpc.Common
	4,  // 29: rpc.bfmap.CreateUser:output_type -> rpc.Common
	4,  // 30: rpc.bfmap.SetProviderToken:output_type -> rpc.Common
	4,  // 31: rpc.bfmap.SetProject:output_type -> rpc.Common
	4,  // 32: rpc.bfmap.SetProjectToken:output_type -> rpc.Common
	4,  // 33: rpc.bfmap.CreateTempMapProjectToken:output_type -> rpc.Common
	4,  // 34: rpc.bfmap.DeleteMapCacheIndexes:output_type -> rpc.Common
	14, // 35: rpc.bfmap.GetCurrentUsage:output_type -> rpc.GetCurrentUsageResp
	4,  // 36: rpc.bfmap.UpdateDbUser:output_type -> rpc.Common
	24, // [24:37] is the sub-list for method output_type
	11, // [11:24] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_bfmap_rpc_proto_init() }
func file_bfmap_rpc_proto_init() {
	if File_bfmap_rpc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_bfmap_rpc_proto_rawDesc), len(file_bfmap_rpc_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bfmap_rpc_proto_goTypes,
		DependencyIndexes: file_bfmap_rpc_proto_depIdxs,
		EnumInfos:         file_bfmap_rpc_proto_enumTypes,
		MessageInfos:      file_bfmap_rpc_proto_msgTypes,
	}.Build()
	File_bfmap_rpc_proto = out.File
	file_bfmap_rpc_proto_goTypes = nil
	file_bfmap_rpc_proto_depIdxs = nil
}
