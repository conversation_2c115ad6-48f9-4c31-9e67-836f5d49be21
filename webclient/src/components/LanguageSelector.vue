<template>
  <q-select
    outlined
    dense
    emit-value
    map-options
    options-dense
    :model-value="props.modelValue"
    @update:model-value="onUpdateValue"
    :options="optionLanguages"
    :option-label="(opt) => opt.nativeName"
    :option-value="(opt) => opt.tag"
    :label="$t('dbMapProviderToken.language')"
    class="pb-4"
    use-input
    hide-selected
    fill-input
    input-debounce="200"
    @filter="filterLanguage"
  >
    <template v-slot:selected-item="scope">
      {{ scope.opt.nativeName }}
    </template>
  </q-select>
</template>

<script setup lang="ts">
import { LanguageTags, type LanguageTag } from "@/utils/languageTag";
import { type QSelectProps } from "quasar";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";

const { locale } = useI18n();

const props = defineProps<{
  modelValue: string;
  clearFixParams?: any[];
}>();

const emit = defineEmits<{
  // eslint-disable-next-line no-unused-vars
  (e: "update:modelValue", value: string): void;
}>();

// 处理值更新
const onUpdateValue = (val: string) => {
  emit("update:modelValue", val);
};

// 过滤语言选项
const optionLanguages = ref<Array<LanguageTag>>(LanguageTags);

// 过滤语言选项的方法
const filterLanguage: QSelectProps["onFilter"] = (
  val: string,
  // eslint-disable-next-line no-unused-vars
  update: (callback: () => void) => void
) => {
  if (val === "") {
    update(() => {
      optionLanguages.value = LanguageTags;
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    optionLanguages.value = LanguageTags.filter(
      (v) =>
        v.tag.toLowerCase().indexOf(needle) > -1 ||
        v.nativeName.toLowerCase().indexOf(needle) > -1
    );
  });
};

// 获取当前 i18n 语言设置
const getCurrentLanguage = (): string => {
  // 使用 vue-i18n 的当前语言设置
  const currentLocale = locale.value;

  // 检查当前 locale 是否在支持的语言列表中
  const exactMatch = LanguageTags.find((tag) => tag.tag === currentLocale);
  if (exactMatch) {
    return exactMatch.tag;
  }

  // 如果没有精确匹配，尝试匹配语言代码部分（如 zh-CN -> zh）
  const langCode = currentLocale.split("-")[0];
  const langMatch = LanguageTags.find((tag) =>
    tag.tag.startsWith(langCode + "-")
  );
  if (langMatch) {
    return langMatch.tag;
  }

  // 默认返回英文
  return "en-US";
};

onMounted(async () => {
  // 如果没有设置值，使用当前 i18n 语言设置作为默认值
  if (!props.modelValue) {
    const currentLang = getCurrentLanguage();
    emit("update:modelValue", currentLang);
  } else {
    // 验证当前值是否在支持的语言列表中
    const isValidLanguage = LanguageTags.some(
      (tag) => tag.tag === props.modelValue
    );
    if (!isValidLanguage) {
      const currentLang = getCurrentLanguage();
      emit("update:modelValue", currentLang);
    }
  }
});
</script>
