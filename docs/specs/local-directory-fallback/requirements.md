# 本地目录回退与语言匹配增强功能需求规格

## 1. 引言

### 1.1 目的

本文档定义 bfmap 系统的本地目录回退功能和语言匹配增强功能的需求规格。该功能旨在提升系统的性能、可靠性和多语言支持能力。

### 1.2 范围

本功能包含两个主要模块：

1. **本地目录回退机制**：在缓存未命中时，自动从本地目录获取瓦片数据
2. **语言匹配增强**：基于 BCP47 标准实现智能语言匹配和 provider token 选择

## 2. 用户故事与功能需求

### 2.1 本地目录回退功能

#### US-001: 缓存未命中时本地目录回退

**作为** 地图服务的使用者
**我希望** 在数据库缓存没有瓦片时，系统能自动从本地目录获取瓦片
**以便** 减少网络请求延迟，提高响应速度

**接受标准 (EARS 格式):**

- WHEN 系统收到地图瓦片请求且数据库缓存中没有对应瓦片 THEN 系统 SHALL 尝试从配置的本地目录获取瓦片
- WHEN 本地目录中存在对应瓦片文件 THEN 系统 SHALL 返回该瓦片并将其缓存到数据库
- WHEN 本地目录中不存在对应瓦片文件 THEN 系统 SHALL 继续原有流程（NATS 或在线服务请求）

#### US-002: 系统过期时的本地目录支持

**作为** 系统管理员
**我希望** 在系统过期时（sysexpired=1），系统仍能提供缓存和本地目录的瓦片
**以便** 在授权过期的情况下维持基本的地图服务

**接受标准 (EARS 格式):**

- WHEN 系统收到 sysexpired=1 的地图瓦片请求 THEN 系统 SHALL 只从缓存和本地目录获取瓦片
- WHEN 缓存和本地目录都没有对应瓦片 THEN 系统 SHALL 返回错误，不得请求在线服务
- WHEN 请求使用 TempMapProjectToken THEN 系统 SHALL 跳过本地目录回退，只使用缓存

#### US-003: NATS消息处理的本地目录支持

**作为** 分布式地图服务节点
**我希望** NATS消息处理器也能支持本地目录回退
**以便** 在集群环境中提供一致的本地目录fallback能力

**接受标准 (EARS 格式):**

- WHEN NATS消息处理器收到地图瓦片请求且缓存中没有对应瓦片 THEN 系统 SHALL 尝试从本地目录获取瓦片
- WHEN 本地目录中存在对应瓦片 THEN 系统 SHALL 响应NATS请求并将瓦片缓存到数据库
- WHEN 本地目录中不存在对应瓦片 THEN 系统 SHALL 不响应NATS请求，让请求方继续在线服务流程
- WHEN NATS处理器处理本地目录查询 THEN 系统 SHALL 不影响现有的NATS响应时间要求

#### US-004: 本地目录配置验证

**作为** 系统管理员
**我希望** 系统能验证本地目录 provider token 的配置
**以便** 确保本地目录路径有效且文件结构正确

**接受标准 (EARS 格式):**

- WHEN 管理员配置本地目录 provider 类型的 token THEN 系统 SHALL 验证 BaseUrl 路径的有效性
- WHEN BaseUrl 路径不存在或无读取权限 THEN 系统 SHALL 返回配置错误
- WHEN 本地目录不符合标准文件结构 THEN 系统 SHALL 在日志中记录警告信息

### 2.2 语言匹配增强功能

#### US-005: SetProviderToken 语言字段验证

**作为** 系统管理员
**我希望** 在创建或更新 provider token 时，系统能验证 Language 字段的格式
**以便** 确保语言标签符合 BCP47 标准

**接受标准 (EARS 格式):**

- WHEN 管理员创建或更新包含 Language 字段的 provider token THEN 系统 SHALL 使用 BCP47 标准验证语言标签格式
- WHEN Language 字段格式无效 THEN 系统 SHALL 返回 InvalidParam 错误并说明具体原因
- WHEN Language 字段有效 THEN 系统 SHALL 将其标准化为规范的 BCP47 格式后存储

#### US-006: 基于语言匹配的 token 选择

**作为** 多语言地图服务的使用者
**我希望** 系统能根据请求的语言智能选择最合适的 provider token
**以便** 获得符合语言偏好的地图内容

**接受标准 (EARS 格式):**

- WHEN 系统需要选择 provider token 且请求包含 lang 参数 THEN 系统 SHALL 使用语言匹配算法评估 token 的适用性
- WHEN 存在语言匹配的 token THEN 系统 SHALL 按照 Confidence 等级排序选择最佳匹配
- WHEN 相同 Confidence 等级存在多个 token THEN 系统 SHALL 按 Priority 字段排序选择
- WHEN 所有 token 的 Confidence 均为"No" THEN 系统 SHALL 返回错误而不使用 fallback

## 3. 非功能性需求

### 3.1 性能需求

- 本地目录瓦片访问响应时间 ≤ 100ms
- 语言匹配算法计算时间 ≤ 10ms
- 本地目录回退不得显著影响现有缓存性能

### 3.2 可靠性需求

- 本地目录访问失败时不得影响正常的在线服务流程
- 语言匹配失败时系统必须有明确的错误处理机制
- 系统必须支持本地目录文件权限不足等异常情况

### 3.3 兼容性需求

- 必须向后兼容现有的 provider token 配置
- 不得影响现有的缓存机制和 NATS 通信
- 支持所有现有的地图类型（roadmap、satellite、hybrid）

### 3.4 安全需求

- 本地目录访问必须限制在配置的 BaseUrl 路径内
- 不得允许通过路径遍历访问系统其他文件
- Language 字段验证必须防止代码注入攻击

## 4. 约束条件

### 4.1 技术约束

- 必须使用 golang.org/x/text/language 包进行语言处理
- 本地目录结构必须遵循现有的 `{BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}` 格式
- 语言匹配算法必须支持 BCP47 标准的所有有效语言标签

### 4.2 业务约束

- TempMapProjectToken 不得使用本地目录回退功能
- 系统过期时（sysexpired=1）不得请求在线地图服务
- 语言匹配失败时不得使用 fallback 机制

### 4.3 操作约束

- 本地目录配置变更必须支持热更新
- 系统必须提供详细的调试日志用于故障排查
- 配置错误必须提供清晰的错误信息

## 5. 数据需求

### 5.1 输入数据

- HTTP 请求参数：x、y、z、mtype、lang、provider、token、sysname、sysexpired
- Provider token 配置：Rid、Provider、Language、BaseUrl、Priority 等
- 本地目录文件：按标准目录结构存储的瓦片文件

### 5.2 输出数据

- 地图瓦片：二进制图像数据（PNG/JPEG 格式）
- 错误信息：符合现有 CommonRespCode 标准的错误响应
- 日志信息：包含语言匹配和本地目录访问的详细日志

### 5.3 数据验证

- Language 字段必须符合 BCP47 标准格式
- 本地目录路径必须是绝对路径且可读取
- 瓦片坐标和缩放级别必须在有效范围内

## 6. 接口需求

### 6.1 API 接口

- SetProviderToken RPC：增强 Language 字段验证
- Map HTTP Handler：集成本地目录回退逻辑
- 现有接口不得破坏向后兼容性

### 6.2 内部接口

- 语言匹配模块：提供 Confidence 评估和 token 排序功能
- 本地目录访问模块：提供文件读取和路径验证功能
- 缓存模块：集成本地目录数据的缓存机制

### 6.3 配置接口

- 支持通过现有的 provider token 配置界面设置 Language 字段
- 支持本地目录类型 provider 的 BaseUrl 配置验证
- 提供语言匹配调试和诊断接口

## 7. 边界条件与异常处理

### 7.1 边界条件

- 空 Language 字段：系统应视为语言不敏感 token
- 无效的本地目录路径：系统应记录错误并跳过该 token
- 极大的瓦片坐标：系统应验证坐标范围有效性

### 7.2 异常处理

- 文件系统 IO 错误：记录日志并继续原有流程
- 语言解析错误：返回明确的错误信息给客户端
- 权限不足错误：提供配置指导信息

### 7.3 降级策略

- 本地目录不可用时自动降级到在线服务
- 语言匹配失败时返回错误（不使用降级）
- 系统过期时只提供缓存和本地目录服务

## 8. 测试需求

### 8.1 单元测试

- 语言匹配算法的各种 BCP47 标签测试
- 本地目录文件访问的边界条件测试
- SetProviderToken 的 Language 字段验证测试

### 8.2 集成测试

- 完整的地图瓦片请求流程测试
- 缓存、本地目录、在线服务的 fallback 链测试
- 多语言环境下的端到端测试

### 8.3 性能测试

- 本地目录访问的性能基准测试
- 语言匹配算法的性能压测
- 大量并发请求下的系统稳定性测试
