# bfmap 本地目录瓦片配置与使用指南

## 概述

本指南介绍如何配置和使用 bfmap 系统的本地目录回退功能。该功能允许您在本地存储地图瓦片，当数据库缓存未命中时，系统会自动从本地目录获取瓦片，从而减少网络请求，提高响应速度。

## 功能特性

- **本地目录回退**：缓存未命中时自动从本地目录获取瓦片
- **多语言支持**：基于 BCP47 标准的智能语言匹配
- **多 provider 支持**：支持 Google Maps、天地图、OpenStreetMap 的本地目录
- **系统过期支持**：在授权过期时仍可提供本地瓦片服务
- **跨平台**：支持 Windows、Linux、macOS 等操作系统

## 支持的 Provider 类型

### 1. Google Maps 本地目录 (ProviderGoogleLocalDirectory)

**特性**：

- 支持多语言瓦片
- 支持 satellite、hybrid、roadmap 三种地图类型
- 通过 Language 字段指定瓦片语言

**目录结构**：

```
{BaseUrl}/
├── satellite/
│   └── {z}/
│       └── {x}/
│           └── {y}.jpg
├── hybrid/
│   └── {z}/
│       └── {x}/
│           └── {y}.jpg
└── roadmap/
    └── {z}/
        └── {x}/
            └── {y}.png
```

### 2. 天地图本地目录 (ProviderTiandituLocalDirectory)

**特性**：

- 仅支持中文（zh-CN）
- 支持 satellite、hybrid、roadmap 三种地图类型
- 其他语言请求会自动映射到中文

**目录结构**：

```
{BaseUrl}/
├── satellite/
│   └── {z}/
│       └── {x}/
│           └── {y}.jpg
├── hybrid/
│   └── {z}/
│       └── {x}/
│           └── {y}.jpg
└── roadmap/
    └── {z}/
        └── {x}/
            └── {y}.png
```

### 3. OpenStreetMap 本地目录 (ProviderOSMLocalDirectory)

**特性**：

- 不支持多语言（语言匹配不生效）
- 仅支持 roadmap 地图类型
- 所有语言请求均返回相同瓦片

**目录结构**：

```
{BaseUrl}/
└── {z}/
    └── {x}/
        └── {y}.png
```

## 配置步骤

### 第一步：准备本地瓦片目录

1. **选择存储位置**：

   **Windows 系统**：

   ```cmd
   mkdir C:\bfmap\tiles
   mkdir C:\bfmap\tiles\google-zh-cn
   mkdir C:\bfmap\tiles\google-en-us
   mkdir C:\bfmap\tiles\tianditu
   mkdir C:\bfmap\tiles\osm
   ```

   **Linux/macOS 系统**：

   ```bash
   mkdir -p /var/lib/bfmap/tiles
   mkdir -p /var/lib/bfmap/tiles/google-zh-cn
   mkdir -p /var/lib/bfmap/tiles/google-en-us
   mkdir -p /var/lib/bfmap/tiles/tianditu
   mkdir -p /var/lib/bfmap/tiles/osm
   ```

2. **按 provider 类型组织目录结构**：

   **Google Maps 目录结构示例**：

   ```
   C:\bfmap\tiles\google-zh-cn\
   ├── satellite\
   │   ├── 16\
   │   │   ├── 12345\
   │   │   │   └── 54321.jpg
   │   │   └── 12346\
   │   └── 17\
   ├── hybrid\
   └── roadmap\
   ```

   **天地图目录结构示例**：

   ```
   C:\bfmap\tiles\tianditu\
   ├── satellite\
   ├── hybrid\
   └── roadmap\
   ```

   **OSM 目录结构示例**：

   ```
   C:\bfmap\tiles\osm\
   ├── 16\
   │   ├── 12345\
   │   │   └── 54321.png
   │   └── 12346\
   └── 17\
   ```

### 第二步：通过管理界面配置 Provider Token

请登录 bfmap 管理界面，在 **地图图源 API 密钥** 页面添加本地目录配置。

参考配置示例：

#### 2.1 Google Maps 本地目录配置

**中文瓦片 token（Windows）**：

- Provider: `ProviderGoogleLocalDirectory`
- Base URL: `C:\bfmap\tiles\google-zh-cn`
- Language: `zh-CN`
- Min Zoom: `1`
- Max Zoom: `18`
- Priority: `10`

**中文瓦片 token（Linux/macOS）**：

- Provider: `ProviderGoogleLocalDirectory`
- Base URL: `/var/lib/bfmap/tiles/google-zh-cn`
- Language: `zh-CN`
- Min Zoom: `1`
- Max Zoom: `18`
- Priority: `10`

#### 2.2 天地图本地目录配置

- Provider: `ProviderTiandituLocalDirectory`
- Base URL: `C:\bfmap\tiles\tianditu` (Windows) 或 `/var/lib/bfmap/tiles/tianditu` (Linux/macOS)
- Language: `zh-CN`
- Min Zoom: `1`
- Max Zoom: `18`
- Priority: `8`

#### 2.3 OpenStreetMap 本地目录配置

- Provider: `ProviderOSMLocalDirectory`
- Base URL: `C:\bfmap\tiles\osm` (Windows) 或 `/var/lib/bfmap/tiles/osm` (Linux/macOS)
- Language: (留空)
- Min Zoom: `1`
- Max Zoom: `18`
- Priority: `7`

### 第三步：验证配置

1. **检查目录结构**：确保瓦片文件按照正确的目录结构存放
2. **测试文件访问**：确保 bfmap 进程能够读取瓦片文件
3. **验证配置**：通过管理界面检查 provider token 配置是否正确

## 使用方式

配置完成后，客户端可以正常请求地图瓦片，系统会自动处理本地目录回退。

## 配置参数说明

### Provider Token 字段

| 字段名     | 类型   | 必填 | 说明                                                        |
| ---------- | ------ | ---- | ----------------------------------------------------------- |
| `name`     | string | 是   | Token 显示名称                                              |
| `provider` | enum   | 是   | Provider 类型（ProviderGoogleLocalDirectory 等）            |
| `baseUrl`  | string | 是   | 本地目录的绝对路径                                          |
| `language` | string | 否   | BCP47 语言标签（Google 支持，天地图固定 zh-CN，OSM 不适用） |
| `minZoom`  | number | 否   | 最小缩放级别（0 表示无限制）                                |
| `maxZoom`  | number | 否   | 最大缩放级别（0 表示无限制）                                |
| `priority` | number | 否   | 优先级（数值越大优先级越高，默认 1）                        |

### 路径格式

| Provider | 路径格式                                | 示例                                           |
| -------- | --------------------------------------- | ---------------------------------------------- |
| Google   | `{baseUrl}/{mapType}/{z}/{x}/{y}.{ext}` | `C:\tiles\google\satellite\16\12345\54321.jpg` |
| 天地图   | `{baseUrl}/{mapType}/{z}/{x}/{y}.{ext}` | `C:\tiles\tianditu\roadmap\16\12345\54321.png` |
| OSM      | `{baseUrl}/{z}/{x}/{y}.{ext}`           | `C:\tiles\osm\16\12345\54321.png`              |

## 故障排除

### 1. 常见问题

**问题：本地目录瓦片无法读取**

```
可能原因：
1. 文件路径错误或不存在
2. 权限不足（特别是Linux/macOS系统）
3. 目录结构不正确
4. baseUrl配置错误

解决方案：
1. 检查瓦片文件是否存在于预期路径
2. 确保bfmap进程有读取权限
3. 验证目录结构是否符合规范
4. 检查baseUrl路径格式（Windows使用反斜杠）
```

**问题：语言匹配不生效**

```
可能原因：
1. Language字段格式不正确
2. Provider类型不支持多语言
3. 语言标签解析失败

解决方案：
1. 使用有效的BCP47语言标签（如zh-CN、en-US）
2. 确认Provider类型：只有Google本地目录完全支持多语言
3. 查看系统日志中的语言匹配结果
```

**问题：性能较慢**

```
可能原因：
1. 硬盘IO性能不足
2. 目录结构导致查找效率低
3. 文件数量过多

解决方案：
1. 使用SSD存储瓦片
2. 避免单个目录下文件过多
3. 调整Priority值优化选择顺序
```

### 2. 启用调试日志

如需查看详细的运行日志，可以使用调试模式启动 bfmap：

```bash
# 启用地图相关的调试日志
./bfmap -debug -debugMap
```

在调试模式下，系统会输出本地目录查询的详细信息，包括：

- 本地目录查询成功或失败的日志
- 语言匹配结果
- 文件读取过程

### 3. 配置验证

**验证 baseUrl 路径**：

```bash
# Windows（命令提示符）
dir "C:\bfmap\tiles\google-zh-cn\satellite\16\12345"

# Linux/macOS
ls -la "/var/lib/bfmap/tiles/google-zh-cn/satellite/16/12345/"
```

**验证权限（Linux/macOS）**：

```bash
# 检查目录权限
ls -ld /var/lib/bfmap/tiles/

# 检查进程用户权限
sudo -u bfmap ls /var/lib/bfmap/tiles/
```

## 最佳实践

1. **渐进式部署**：

   - 先配置小范围测试区域的瓦片
   - 验证功能正常后再扩展到全部区域

2. **合理的目录结构**：

   - 避免单个目录下文件过多（建议每目录少于 1000 个文件）
   - 使用合适的缩放级别范围避免存储过多细节瓦片

3. **多语言规划**：

   - 对于 Google Maps，预先规划需要支持的语言
   - 为不同语言的瓦片使用不同的 baseUrl 目录

4. **优先级设置**：

   - 设置合理的 Priority 值，高质量的瓦片使用更高优先级
   - 同一 provider 的不同语言 token 可设置不同优先级

5. **存储规划**：
   - 评估瓦片存储空间需求
   - 选择性能合适的存储设备
   - 制定瓦片更新策略

## 参考资料

- [BCP47 语言标签规范](https://tools.ietf.org/html/bcp47)
- [Google Maps 瓦片坐标系统](https://developers.google.com/maps/documentation/javascript/coordinates)
- [OpenStreetMap 瓦片服务器](https://wiki.openstreetmap.org/wiki/Tile_servers)
