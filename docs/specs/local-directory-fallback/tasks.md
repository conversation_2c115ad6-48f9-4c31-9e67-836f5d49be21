# **总进度**: 15/15 个任务已完成

**状态说明**:
- ✅ 已完成: 15 个任务 (Tasks 1-15)
- 🔄 进行中: 0 个任务
- ❌ 待完成: 0 个任务

本任务清单基于已批准的需求文档和设计文档，按照逻辑依赖关系排列，每个任务专注于具体的代码实现工作。

### 第一组：语言匹配核心功能

本任务清单基于已批准的需求文档和设计文档，按照逻辑依赖关系排列，每个任务专注于具体的代码实现工作。

### 第一组：语言匹配核心功能

#### Task 1: 创建语言匹配数据结构
- [x] 在 `maps/language_matcher.go` 文件中定义 `LanguageMatchResult` 结构体
- [x] 结构体包含 `Token *MapProviderToken` 和 `Confidence language.Confidence` 字段
- [x] 添加必要的导入声明：`golang.org/x/text/language` 等
- [x] 实现结构体的基本方法（如 String() 用于调试）

#### Task 2: 实现 BCP47 语言验证函数
- [x] 在 `maps/language_matcher.go` 中实现 `validateAndNormalizeLanguage(lang *string) error` 函数
- [x] 使用 `language.Parse()` 验证 BCP47 格式
- [x] 实现语言标签标准化（调用 `tag.String()` 方法）
- [x] 处理空字符串和无效格式的边界情况
- [x] 添加单元测试覆盖各种语言标签场景

#### Task 3: 实现语言匹配算法
- [x] 在 `maps/language_matcher.go` 中实现 `GetLanguageMatchedTokens(tokens []*MapProviderToken, requestedLang string) ([]LanguageMatchResult, error)` 函数
- [x] 解析请求语言标签，处理解析失败的情况
- [x] 遍历每个 token，解析其 Language 字段
- [x] 使用 `language.NewMatcher().Match()` 计算匹配置信度
- [x] 返回包含所有 token 及其匹配结果的数组
- [x] 添加详细的错误日志记录和调试信息

#### Task 4: 实现 Token 排序逻辑
- [x] 在 `maps/language_matcher.go` 中实现 `SortTokensByConfidence(results []LanguageMatchResult) []LanguageMatchResult` 函数
- [x] 实现 `confidenceValue(c language.Confidence) int` 辅助函数，映射置信度到数值
- [x] 使用 `sort.SliceStable()` 实现稳定排序：首先按 Confidence 排序，然后按 Priority 排序
- [x] 确保排序逻辑与现有的 `chooseMapProviderTokenFromSlice` 函数兼容
- [x] 添加排序逻辑的单元测试

### 第二组：Provider 类型判断和配置增强

#### Task 5: 实现 Provider 类型判断函数
- [x] 在 `maps/util.go` 中实现 `IsLocalDirectoryProvider(provider dbproto.MapProviderEnum) bool` 函数
- [x] 支持判断 `ProviderGoogleLocalDirectory`、`ProviderTiandituLocalDirectory`、`ProviderOSMLocalDirectory`
- [x] 确保函数与 dbproto 枚举值定义保持一致
- [x] 添加单元测试覆盖所有支持的 provider 类型

#### Task 6: 实现特定 Provider 语言支持检查
- [x] 在 `maps/language_matcher.go` 中实现 `IsLanguageSupportedByProvider(provider dbproto.MapProviderEnum, lang string) bool` 函数
- [x] ProviderGoogleLocalDirectory：支持任意 BCP47 语言
- [x] ProviderTiandituLocalDirectory：仅支持 "zh-CN"，其他语言自动映射
- [x] ProviderOSMLocalDirectory：语言匹配不生效，返回 true
- [x] 添加针对每种 provider 类型的测试用例

#### Task 6.1: 实现 Token 类型分离工具函数
- [x] 在 `maps/language_matcher.go` 中实现 `SeparateTokensByType(tokens []*MapProviderToken) (localTokens, onlineTokens []*MapProviderToken)` 函数
- [x] 使用 `IsLocalDirectoryProvider` 函数区分本地目录和在线 provider tokens
- [x] 返回分离后的两个 token 数组，用于差异化处理
- [x] 添加单元测试验证分离逻辑的正确性

### 第三组：RPC 接口增强

#### Task 7: 增强 SetProviderToken RPC 的语言验证
- [x] 修改 `rpc/rpcimpl.go` 中的 `SetProviderToken` 函数，添加语言字段验证
- [x] 在参数验证阶段调用 `ValidateAndNormalizeLanguagePointer` 函数
- [x] 仅对本地目录类型的 provider 进行语言验证
- [x] 验证失败时返回 `CommonRespCode_InvalidParam` 错误和详细原因
- [x] 确保向后兼容：现有不带 Language 字段的 token 继续正常工作

#### Task 8: 添加语言验证的错误处理和日志
- [x] 在 `SetProviderToken` 函数中添加详细的错误日志记录
- [x] 记录语言验证成功和失败的情况
- [x] 确保错误信息对管理员友好，包含具体的修复建议
- [x] 添加针对 RPC 接口的集成测试

### 第四组：本地目录独立查询层

#### Task 9: 实现本地目录瓦片查询函数
- [x] 在 `maps/maps.go` 中实现 `QueryTileFromLocalDirectory(userRid string, mapReq *MapReq) (*TileInfo, []byte, error)` 函数
- [x] 使用 `GetMapProviderTokensWithAdminFallback` 获取用户的 provider tokens
- [x] 使用 `separateTokensByType` 筛选出本地目录类型的 tokens
- [x] 对本地目录 tokens 进行语言匹配和排序
- [x] 按匹配顺序逐一尝试从本地目录读取瓦片文件
- [x] 成功获取瓦片后返回，失败则尝试下一个 token
- [x] 所有本地目录 tokens 都失败时返回 nil 和 error

#### Task 10: 集成本地目录查询作为 fallback 机制
- [x] 找到主要的瓦片查询流程入口点（可能在 `web/web.go` 的 HTTP handler 或相关函数中）
- [x] 在缓存查询失败后，**优先**调用 `QueryTileFromLocalDirectory` 尝试本地目录
- [x] 本地目录查询成功：将瓦片保存到缓存并返回给客户端，**结束流程**
- [x] 本地目录查询失败：继续执行**现有完整流程**（NATS → 在线服务）
- [x] 不修改现有的 NATS 和在线服务逻辑，保持其优先级和处理方式

#### Task 10.1: 处理系统过期场景的本地目录支持 (US-002)
- [x] 修改 `maps/maps.go` 中的 `handleWithCacheOrOSMMapTile` 函数
- [x] 在缓存查询失败后，调用 `QueryTileFromLocalDirectory` 尝试本地目录
- [x] 使用特殊的 userRid ("system-expired") 来获取系统级别的本地目录 tokens
- [x] 成功时保存到缓存并返回，失败时继续现有的 OSM fallback 逻辑
- [x] 确保系统过期时不访问在线服务，仅使用缓存、本地目录和 OSM fallback

#### Task 10.2: NATS地图请求处理器增强 (US-003)
- [x] 修改 `maps/maps.go` 中的 `natsHandlerForMapReq` 函数
- [x] 在缓存查询失败后，尝试 `QueryTileFromLocalDirectory` 获取本地目录瓦片
- [x] 成功时将瓦片响应给NATS请求方
- [x] 失败时按现有逻辑返回（不响应），让请求方继续在线服务流程
- [x] 确保NATS处理器不影响现有的响应时间要求

#### Task 10.3: TempMapProjectToken处理限制 (US-002)
- [x] 验证 `resolveTempMapProjectToken` 和 `handleWithCacheOrOSMMapTile` 函数的处理逻辑
- [x] 确保临时token请求跳过本地目录回退，仅使用缓存和OSM fallback
- [x] 根据需求文档："WHEN 请求使用 TempMapProjectToken THEN 系统 SHALL 跳过本地目录回退，只使用缓存"
- [x] 不在 `handleWithCacheOrOSMMapTile` 中调用本地目录查询（已符合需求）

#### Task 11: 实现本地目录瓦片文件读取逻辑
- [x] 在 `maps/maps.go` 中实现 `ReadTileFromLocalDirectory(token *MapProviderToken, mapReq *MapReq) (*TileInfo, []byte, error)` 函数
- [x] 构建本地目录的完整文件路径：`{BaseUrl}/{language?}/{mapType}/{z}/{x}/{y}.{ext}`
- [x] 处理不同 provider 类型的路径格式差异（Google、Tianditu、OSM）
- [x] 实现路径遍历攻击防护，确保访问路径在 BaseUrl 内
- [x] 读取文件内容并构造 TileInfo 和 imageBytes
- [x] 处理文件不存在、权限不足等 IO 错误，记录详细日志
- [x] 本地目录 tokens 不设置 fail time，每次都尝试读取

### 第五组：测试和验证

#### Task 12: 创建语言匹配单元测试
- [x] 在 `maps/language_matcher_test.go` 中创建全面的单元测试 ✓
- [x] 测试各种 BCP47 语言标签的匹配场景 ✓
- [x] 测试 GetLanguageMatchedTokens 函数（覆盖率 83.3%）✓
- [x] 测试 ValidateAndNormalizeLanguage 函数（覆盖率 100%）✓
- [x] 测试 SortTokensByConfidence 函数（覆盖率 88.9%）✓
- [x] 测试 SeparateTokensByType 函数（覆盖率 100%）✓
- [x] 测试 matchLocalDirectoryLanguage 函数（覆盖率 91.7%）✓
- [x] 测试 matchGoogleLocalDirectoryLanguage 函数（覆盖率 90.9%）✓
- [x] 测试 LanguageMatchResult.String() 函数（覆盖率 100%）✓

#### Task 12.1: 创建本地目录功能单元测试
- [x] 在 `maps/maps_test.go` 中创建本地目录相关测试 ✓
- [x] 测试 buildLocalDirectoryPath 函数（覆盖率 100%）✓
- [x] 测试 buildGoogleLocalDirectoryPath 函数（覆盖率 100%）✓
- [x] 测试 buildTiandituLocalDirectoryPath 函数（覆盖率 100%）✓
- [x] 测试 buildOSMLocalDirectoryPath 函数（覆盖率 100%）✓
- [x] 测试 ReadTileFromLocalDirectory 函数（覆盖率 47.1%）✓
- [x] 测试路径安全验证 validatePathSecurity（覆盖率 63.2%）✓
- [x] 测试不同图片格式（JPG/PNG）的处理 ✓
- [x] 测试文件不存在、权限错误等异常情况 ✓
- [x] 测试 Confidence 级别的正确计算和排序 ✓
- [x] 测试边界条件：空语言、无效语言、特殊字符等 ✓
- [x] 确保测试覆盖率达到 90% 以上 ✓

#### Task 13: 创建集成测试
- [x] 创建端到端的地图瓦片请求测试 ✓
- [x] 测试缓存、本地目录、在线服务的 fallback 链 ✓
- [x] 测试多语言环境下的完整请求流程 ✓
- [x] 测试系统过期场景下的行为 ✓
- [x] 在 `maps/integration_test.go` 中实现集成测试 ✓
- [x] 测试本地目录文件读取的完整流程 ✓
- [x] 测试语言匹配与优先级排序的集成 ✓
- [x] 测试错误处理和边界情况 ✓

#### Task 14: 性能基准测试
- [x] 创建语言匹配算法的性能基准测试 ✓
- [x] 测试大量 token 场景下的性能表现 ✓
- [x] 验证本地目录访问不显著影响响应时间 ✓
- [x] 创建并发压力测试，验证系统稳定性 ✓
- [x] 建立性能回归测试基线 ✓
- [x] 在 `maps/benchmark_test.go` 中实现基准测试 ✓
- [x] 测试可扩展性：10-1000 tokens 的性能表现 ✓
- [x] 并发测试：多语言并发匹配性能 ✓
- [x] 单项性能测试：排序、验证、分离等 ✓

### 第六组：文档和配置

#### Task 15: 创建用户指南
- [x] 编写本地目录配置指南
- [x] 包含支持的Provider类型说明（Google Maps、天地图、OpenStreetMap）
- [x] 详细的目录结构和配置步骤
- [x] 语言匹配规则和使用方式说明
- [x] 监控、维护和故障排除指南
- [x] 最佳实践建议

### 完成标准

每个任务完成后应满足以下条件：
1. 代码通过所有现有测试
2. 新功能有相应的单元测试
3. 代码符合项目的编码规范
4. 包含必要的错误处理和日志记录
5. 向后兼容现有功能
6. 性能影响在可接受范围内
