# Local Directory Fallback 设计文档

## 概述

本设计文档定义了地图瓦片本地目录回退功能和语言匹配增强功能的技术实现方案。该功能将在缓存未命中时首先尝试本地目录 provider，然后基于 BCP47 语言标准进行智能的 provider token 选择。

## 架构

### 系统架构图

```mermaid
graph TD
    A[HTTP请求] --> B[MapHttpHandler]
    B --> C[parseMapReq - BCP47解析]
    C --> D[查询缓存]
    D --> E{缓存命中?}
    E -->|是| F[返回缓存瓦片]
    E -->|否| G[QueryTileFromLocalDirectory - 本地目录fallback]
    G --> H[GetLanguageMatchedTokens - 语言匹配]
    H --> I[SortTokensByConfidence - 排序]
    I --> J[循环尝试本地目录Token]
    J --> K[ReadTileFromLocalDirectory - 读取文件]
    K --> L{读取成功?}
    L -->|是| M[保存到缓存并返回]
    L -->|否| N[尝试下一个本地Token]
    N --> J
    J --> O{所有本地Token失败?}
    O -->|是| P{系统过期?}
    P -->|是| Q[返回错误]
    P -->|否| R[继续现有流程: NATS → 在线服务]
    R --> S[返回结果或错误]
```

### 组件设计

#### 1. 语言匹配引擎 (Language Matching Engine)

**位置**: `maps/language_matcher.go`

**职责**:

- 基于 BCP47 标准匹配 provider token 的 Language 字段
- 使用 golang.org/x/text/language 包的 Confidence 级别
- 返回按匹配质量排序的 token 列表

#### 2. 本地目录 Fallback 层 (Local Directory Fallback Layer)

**位置**: `maps/maps.go` - 新增独立函数

**职责**:

- 作为缓存未命中后的第一级 fallback 机制
- 整合语言匹配算法，优先尝试匹配的本地目录 token
- 成功时结束查询流程，失败时不影响现有的 NATS 和在线服务逻辑
- 纯粹的附加层，不修改现有业务流程的优先级

#### 3. Provider Token 语言验证器

**位置**: `rpc/rpcimpl.go` - `SetProviderToken`函数增强

**职责**:

- 验证 Provider Token 的 Language 字段 BCP47 格式
- 标准化语言标签

#### 4. 系统过期场景处理器 (System Expired Handler)

**位置**: `maps/maps.go` - `handleWithCacheOrOSMMapTile`函数增强

**职责**:

- 处理 `sysexpired=1` 参数的特殊场景
- 在系统过期时仅使用缓存和本地目录，禁用在线服务
- 提供授权过期情况下的基本地图服务保障

#### 5. NATS 消息处理器增强 (NATS Handler Enhancement)

**位置**: `maps/maps.go` - `natsHandlerForMapReq`函数增强

**职责**:

- 在 NATS 消息处理流程中集成本地目录回退
- 缓存查询失败后，尝试本地目录查询
- 成功时响应 NATS 请求，失败时保持现有逻辑（不响应让请求方继续在线服务）
- 不影响现有 NATS 响应时间要求

#### 6. 临时 Token 限制处理器 (Temp Token Restriction Handler)

**位置**: `maps/maps.go` - 现有 token 处理逻辑验证

**职责**:

- 确保`TempMapProjectToken`类型的请求跳过本地目录回退
- 维护临时 token 仅使用缓存的限制策略
- 验证现有`resolveTempMapProjectToken`和`handleWithCacheOrOSMMapTile`的处理流程符合需求

## 组件详细设计

### 1. 语言匹配引擎设计

```go
// LanguageMatchResult represents a language matching result
type LanguageMatchResult struct {
    Token      *MapProviderToken
    Confidence language.Confidence
}

// GetLanguageMatchedTokens returns tokens sorted by language matching confidence
func GetLanguageMatchedTokens(tokens []*MapProviderToken, requestedLang string) ([]LanguageMatchResult, error)

// SortTokensByConfidence sorts tokens by confidence level and then by priority
func SortTokensByConfidence(results []LanguageMatchResult) []LanguageMatchResult
```

**匹配逻辑**:

1. 解析请求的语言标签 (通过现有的 parseMapReq 函数)
2. 对每个 token 的 Language 字段进行 BCP47 解析
3. 使用 language.NewMatcher().Match()计算匹配度
4. 返回按 Confidence 排序的结果

**Confidence 级别处理**:

- `Exact`: 完全匹配 (如 "zh-CN" 匹配 "zh-CN")
- `High`: 高度匹配 (如 "zh" 匹配 "zh-CN")
- `Low`: 低度匹配 (如 "en" 匹配 "en-US")
- `No`: 无匹配 (降级处理)

### 2. 本地目录回退增强设计

**现有 QueryTileFromProvider 函数增强**:

参考现有的`QueryTileFromProvider`函数逻辑，该函数已经实现了以下模式：

1. 获取所有可用 tokens：`GetMapProviderTokensWithAdminFallback(userRid, mapReq.Provider)`
2. 选择起始 token：`chooseMapProviderTokenFromSlice(providerTokens)`
3. 循环尝试 tokens：`for ; index < len(providerTokens); index++`

我们需要修改这个模式来支持语言匹配：

```go
func QueryTileFromProvider(
    userRid string,
    mapReq *MapReq,
    needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error) {
    // 1. 获取所有可用tokens (现有逻辑)
    providerTokens, err := GetMapProviderTokensWithAdminFallback(userRid, mapReq.Provider)

    // 2. 新增: 语言匹配和排序
    languageMatchedTokens, err := GetLanguageMatchedTokens(providerTokens, mapReq.Lang)

    // 3. 新增: 按confidence排序
    sortedTokens := SortTokensByConfidence(languageMatchedTokens)

    // 4. 按顺序循环尝试 (参考现有chooseMapProviderTokenFromSlice逻辑)
    for _, matchResult := range sortedTokens {
        token := matchResult.Token
        if !token.IsValid() {
            continue
        }
        if token.MinZoom > 0 && mapReq.Z < token.MinZoom {
            continue
        }
        if token.MaxZoom > 0 && mapReq.Z > token.MaxZoom {
            continue
        }

        // 尝试获取瓦片 (现有ReqMapFromProviderWithToken逻辑)
        tileInfo, imageBytes, err = ReqMapFromProviderWithToken(token, mapReq, needCreateNewIndex)
        if err == nil {
            return tileInfo, imageBytes, nil
        }

        // 失败时标记token (现有逻辑)
        GlobalMapsManager.SetMapProviderTokenLastFailTime(token.TokenRid, bfutil.CurrentUTCTime())
    }

    return nil, nil, errors.New("failed to request with all provider tokens and admin fallback")
}
```

### 3. SetProviderToken 语言验证增强

**rpc/rpcimpl.go 中 SetProviderToken 函数增强**:

在现有的`SetProviderToken`函数中添加语言字段验证：

```go
func (RpcImpl) SetProviderToken(req *SetProviderTokenReq) (*Common, error) {
    t := req.ProviderToken

    // 现有的权限检查...

    // 新增: 语言字段验证
    if isLocalDirectoryProvider(t.Provider) && t.Language != "" {
        if err := validateAndNormalizeLanguage(&t.Language); err != nil {
            return &Common{
                Code: CommonRespCode_InvalidParam,
                Reason: "invalid language format: " + err.Error(),
            }, nil
        }
    }

    // 现有的业务逻辑继续...
    switch req.Code {
    case 1: // create
        // 现有create逻辑...
    case 2: // update
        // 现有update逻辑...
    }
}

func validateAndNormalizeLanguage(lang *string) error {
    if *lang == "" {
        return nil
    }

    tag, err := language.Parse(*lang)
    if err != nil {
        return fmt.Errorf("invalid BCP47 language tag: %w", err)
    }

    *lang = tag.String() // 标准化格式
    return nil
}

func isLocalDirectoryProvider(provider dbproto.MapProviderEnum) bool {
    return provider == dbproto.MapProviderEnum_ProviderGoogleLocalDirectory ||
           provider == dbproto.MapProviderEnum_ProviderTiandituLocalDirectory ||
           provider == dbproto.MapProviderEnum_ProviderOSMLocalDirectory
}
```

### 4. 系统过期场景处理增强

**handleWithCacheOrOSMMapTile 函数增强**:

在现有的系统过期处理函数中添加本地目录支持：

```go
func handleWithCacheOrOSMMapTile(w http.ResponseWriter, r *http.Request, mapReq *MapReq) {
    var imageBytes []byte
    var tileInfo *TileInfo
    var err error

    // 1. 首先尝试缓存 (现有逻辑)
    tileInfo, imageBytes, err = QueryTileFromKeyValueDb(mapReq)
    if err == nil {
        respondImageBytes(w, r, mapReq, tileInfo, imageBytes)
        return
    }

    // 2. 新增: 尝试本地目录 fallback
    // 注意: 系统过期时使用固定的userRid，不使用实际项目的userRid
    tileInfo, imageBytes, err = QueryTileFromLocalDirectory("system-expired", mapReq)
    if err == nil {
        // 保存到缓存
        savedTileInfo, saveErr := SaveTileToDb(mapReq, imageBytes, false)
        if saveErr == nil {
            tileInfo = savedTileInfo
        }
        respondImageBytes(w, r, mapReq, tileInfo, imageBytes)
        return
    }

    // 3. 最后尝试 OSM fallback (现有逻辑)
    if mapReq.Provider == int(dbproto.MapProviderEnum_ProviderOSM) &&
       mapReq.MapType == MapTypeRoadmap {
        imageBytes, err := ReqMapTileFromDefaultOSM(mapReq.X, mapReq.Y, mapReq.Z)
        if err == nil {
            respondImageBytes(w, r, mapReq, nil, imageBytes)
            return
        }
    }

    // 4. 所有方法都失败，返回错误
    respondHttpError(w, r, "tile not found in cache or local directory", http.StatusNotFound)
}
```

**设计要点**:

- 系统过期时禁止访问在线服务，仅使用缓存、本地目录和 OSM fallback
- 使用特殊的`userRid`("system-expired")来获取系统级别的本地目录 tokens
- 保持现有的错误处理和响应逻辑

### 5. NATS 消息处理器增强设计

**natsHandlerForMapReq 函数增强**:

在现有的 NATS 消息处理中添加本地目录支持：

```go
func natsHandlerForMapReq(ctx context.Context, mapReq *MapReq) {
    // 1. 首先尝试缓存 (现有逻辑)
    tileInfo, imageBytes, err := QueryTileFromKeyValueDb(mapReq)
    if err == nil {
        // 响应NATS请求 (现有逻辑)
        respondToNatsMapReq(ctx, mapReq, tileInfo, imageBytes)
        return
    }

    // 2. 新增: 尝试本地目录查询
    tileInfo, imageBytes, err = QueryTileFromLocalDirectory(mapReq.UserRid, mapReq)
    if err == nil {
        // 保存到缓存
        savedTileInfo, saveErr := SaveTileToDb(mapReq, imageBytes, false)
        if saveErr == nil {
            tileInfo = savedTileInfo
        }
        // 响应NATS请求
        respondToNatsMapReq(ctx, mapReq, tileInfo, imageBytes)
        return
    }

    // 3. 失败时不响应，让请求方继续在线服务流程 (现有逻辑)
    // NATS处理器不响应失败情况，由请求方决定是否使用在线服务
}
```

**设计要点**:

- 在缓存查询失败后立即尝试本地目录
- 成功时响应 NATS 请求并保存到缓存
- 失败时保持现有逻辑：不响应，让请求方继续在线服务
- 不影响 NATS 通信的响应时间要求

### 6. 临时 Token 限制处理器设计

**现有处理流程验证**:

验证`TempMapProjectToken`的处理路径确保跳过本地目录：

```go
// resolveTempMapProjectToken 调用路径分析
func resolveTempMapProjectToken(...) {
    // 最终调用 handleWithCacheOrOSMMapTile
    // 这个函数目前只使用缓存和OSM fallback，不包含本地目录查询
    // 符合需求：临时token跳过本地目录回退
}
```

**设计要点**:

- 临时 token 处理路径天然跳过本地目录（通过`handleWithCacheOrOSMMapTile`）
- 仅在`handleWithCacheOrOSMMapTile`中添加本地目录支持不会影响临时 token
- 确保`TempMapProjectToken`请求始终使用缓存和 OSM fallback 路径
- 验证现有架构已满足 US-002 的限制需求

## 数据模型

### DbMapProviderToken 增强使用

利用现有的`DbMapProviderToken.Language`字段 (field 20):

```proto
message DbMapProviderToken {
    // ... 现有字段
    // for local directory, the language of tiles in the directory, only work for ProviderGoogleLocalDirectory,
    // ProviderOSMLocalDirectory is not support multi language, ProviderTiandituLocalDirectory only support zh-CN
    string Language = 20;
}
```

**使用约定**:

- 必须使用 BCP47 格式 (如 "zh-CN", "en-US", "ja")
- ProviderGoogleLocalDirectory: 支持任意 BCP47 语言
- ProviderTiandituLocalDirectory: 仅支持 "zh-CN"
- ProviderOSMLocalDirectory: 语言匹配不生效 (保持现有逻辑)

## 错误处理

### 语言解析错误

- 请求语言解析失败: 使用默认匹配逻辑
- Token 语言解析失败: 跳过该 token，记录警告日志

### Token 选择失败

- 无可用 token: 返回现有错误信息
- 所有 token 失败: 返回最后一个错误

### 本地目录访问失败

- 目录不存在: 跳过该 token
- 权限错误: 跳过该 token，记录错误日志

## 测试策略

### 单元测试

1. **语言匹配测试**:

   - 精确匹配测试
   - 部分匹配测试
   - 无匹配测试
   - 无效语言标签测试

2. **Token 选择测试**:

   - 优先级排序测试
   - Confidence 排序测试
   - 混合排序测试

3. **BCP47 验证测试**:
   - 有效语言标签
   - 无效语言标签
   - 空值处理

### 集成测试

1. **端到端瓦片请求测试**:

   - 缓存命中场景
   - 本地目录回退场景
   - 语言匹配场景

2. **RPC 接口测试**:
   - SetProviderToken 语言验证
   - 错误响应验证

### 性能测试

1. **语言匹配性能**:

   - 大量 token 的匹配性能
   - 内存使用分析

2. **缓存性能影响**:
   - 回退机制对响应时间的影响

## 实现细节

### BCP47 语言解析集成

利用现有的`parseMapReq`函数中的 BCP47 解析逻辑:

```go
// 现有代码在 maps/maps.go
func parseMapReq(r *http.Request) (*MapReq, error) {
    // ... 现有解析逻辑

    // 现有的BCP47解析
    lang := r.FormValue("lang")
    if lang != "" {
        if langTag, err := language.All.Parse(lang); err == nil {
            lang = langTag.String()
        }
    }

    // 设置到MapReq.Lang字段
    mapReq.Lang = lang
}
```

### 排序逻辑设计

参考现有的`chooseMapProviderTokenFromSlice`函数逻辑，但修改为按 confidence 排序:

```go
func SortTokensByConfidence(results []LanguageMatchResult) []LanguageMatchResult {
    sort.SliceStable(results, func(i, j int) bool {
        // 首先按Confidence排序 (Exact > High > Low > No)
        if results[i].Confidence != results[j].Confidence {
            return int(results[i].Confidence) > int(results[j].Confidence)
        }
        // 相同Confidence下按Priority排序
        return results[i].Token.Priority > results[j].Token.Priority
    })
    return results
}
```

### 现有 Token 选择逻辑分析

参考现有的`chooseMapProviderTokenFromSlice`实现：

- 该函数按 Priority 分组
- 在相同 Priority 内随机选择
- 我们需要修改为按 Confidence 排序，然后按 Priority 排序

## 配置说明

### Provider 特定配置

1. **ProviderGoogleLocalDirectory**:

   - Language 字段支持任意 BCP47 语言
   - 目录结构: `/{mapType}/{z}/{x}/{y}.{format}`

2. **ProviderTiandituLocalDirectory**:

   - Language 字段固定为 "zh-CN"
   - 其他语言请求自动映射到 "zh-CN"

3. **ProviderOSMLocalDirectory**:
   - Language 字段无效果 (OSM 不支持多语言)
   - 保持现有目录结构

### 回退策略配置

本地目录回退优先级:

1. 精确语言匹配的本地目录 token
2. 高度语言匹配的本地目录 token
3. 低度语言匹配的本地目录 token
4. 在线 provider token (现有逻辑)

## 兼容性考虑

### 向后兼容

- 现有不带 Language 字段的 token 继续正常工作
- 现有 API 调用不受影响
- 缓存机制保持不变

### 升级策略

1. 新增语言匹配功能默认启用
2. 现有 token 可逐步添加 Language 字段
3. 不影响现有地图瓦片服务

## 监控和日志

### 关键指标

- 语言匹配命中率
- 本地目录回退成功率
- Token 选择分布

### 日志记录

- 语言匹配结果 (DEBUG 级别)
- Token 选择过程 (DEBUG 级别)
- 回退机制触发 (INFO 级别)
- 错误和异常 (WARN/ERROR 级别)
