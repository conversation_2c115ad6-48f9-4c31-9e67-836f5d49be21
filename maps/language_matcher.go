package maps

import (
	"bfmap/dbproto"
	"fmt"
	"sort"

	"golang.org/x/text/language"
)

// LanguageMatchResult represents a language matching result
type LanguageMatchResult struct {
	Token      *MapProviderToken
	Confidence language.Confidence
}

// String returns a string representation of LanguageMatchResult for debugging
func (lmr LanguageMatchResult) String() string {
	if lmr.Token == nil {
		return fmt.Sprintf("LanguageMatchResult{Token: nil, Confidence: %v}", lmr.Confidence)
	}
	return fmt.Sprintf("LanguageMatchResult{TokenRid: %s, Provider: %v, Language: %s, Confidence: %v}",
		lmr.Token.TokenRid, lmr.Token.Provider, lmr.Token.Language, lmr.Confidence)
}

// GetLanguageMatchedTokens returns tokens sorted by language matching confidence
// It matches the requestedLang against each token's Language field using BCP47 standards
func GetLanguageMatchedTokens(tokens []*MapProviderToken, requestedLang string) ([]LanguageMatchResult, error) {
	if len(tokens) == 0 {
		return nil, fmt.Errorf("no tokens provided")
	}

	results := make([]LanguageMatchResult, 0, len(tokens))

	// Parse the requested language tag
	var requestedTag language.Tag
	if requestedLang != "" {
		var err error
		requestedTag, err = language.Parse(requestedLang)
		if err != nil {
			// If parsing fails, treat as empty language for fallback matching
			requestedTag = language.Und
		}
	} else {
		requestedTag = language.Und
	}

	// Process each token for language matching
	for _, token := range tokens {
		if token == nil {
			continue
		}

		confidence := matchTokenLanguage(token, requestedTag)

		results = append(results, LanguageMatchResult{
			Token:      token,
			Confidence: confidence,
		})
	}

	if len(results) == 0 {
		return nil, fmt.Errorf("no valid tokens found for language matching")
	}

	return results, nil
}

// matchTokenLanguage performs language matching between requested language and token's language
func matchTokenLanguage(token *MapProviderToken, requestedTag language.Tag) language.Confidence {
	// Special handling for local directory providers
	if IsLocalDirectoryProvider(token.Provider) {
		return matchLocalDirectoryLanguage(token, requestedTag)
	}

	// For online providers, language matching is not applicable
	// Return High confidence to maintain existing behavior
	return language.High
}

// matchLocalDirectoryLanguage handles language matching for local directory providers
func matchLocalDirectoryLanguage(token *MapProviderToken, requestedTag language.Tag) language.Confidence {
	// Handle different local directory provider types
	switch token.Provider {
	case dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
		// OSM local directory does not support multi-language
		// Return High confidence to maintain compatibility
		return language.High

	case dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
		// Tianditu local directory only supports zh-CN
		if requestedTag == language.Und {
			return language.High
		}

		zhCN, _ := language.Parse("zh-CN")
		zh, _ := language.Parse("zh")

		switch requestedTag {
		case zhCN:
			// If requested tag is Chinese or Simplified Chinese, return Exact confidence
			return language.Exact
		case zh:
			// If requested tag is Chinese base, return High confidence
			return language.High
		default:
			// For any other language, return Low confidence
			return language.Low
		}

	case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
		// Google local directory supports multiple languages
		return matchGoogleLocalDirectoryLanguage(token, requestedTag)

	default:
		return language.High
	}
}

// matchGoogleLocalDirectoryLanguage handles language matching for Google local directory
func matchGoogleLocalDirectoryLanguage(token *MapProviderToken, requestedTag language.Tag) language.Confidence {
	tokenLanguage := getTokenLanguage(token)

	// If token has no language specified, return high confidence for backward compatibility
	if tokenLanguage == "" {
		return language.High
	}

	// Parse token's language tag
	tokenTag, err := language.Parse(tokenLanguage)
	if err != nil {
		// If token's language is invalid, return low confidence
		return language.Low
	}

	// If no specific language requested, return high confidence
	if requestedTag == language.Und {
		return language.High
	}

	// Use language matcher to determine confidence
	matcher := language.NewMatcher([]language.Tag{tokenTag})
	_, _, confidence := matcher.Match(requestedTag)

	return confidence
}

// getTokenLanguage extracts the Language field from MapProviderToken
func getTokenLanguage(token *MapProviderToken) string {
	return token.Language
}

// SortTokensByConfidence sorts tokens by confidence level and then by priority
func SortTokensByConfidence(results []LanguageMatchResult) []LanguageMatchResult {
	if len(results) == 0 {
		return results
	}

	// Create a copy to avoid modifying the original slice
	sorted := make([]LanguageMatchResult, len(results))
	copy(sorted, results)

	sort.SliceStable(sorted, func(i, j int) bool {
		// First sort by Confidence (Exact > High > Low > No)
		if sorted[i].Confidence != sorted[j].Confidence {
			return int(sorted[i].Confidence) > int(sorted[j].Confidence)
		}

		// For same confidence, sort by Priority (higher priority first)
		return sorted[i].Token.Priority > sorted[j].Token.Priority
	})

	return sorted
}

// ValidateAndNormalizeLanguage validates a BCP47 language tag and returns the normalized form
func ValidateAndNormalizeLanguage(lang string) (string, error) {
	if lang == "" {
		return "", fmt.Errorf("language cannot be empty")
	}

	tag, err := language.Parse(lang)
	if err != nil {
		return "", fmt.Errorf("invalid BCP47 language tag: %v", err)
	}

	// Return the canonical string representation
	return tag.String(), nil
}

// SeparateTokensByType separates tokens into local directory and online provider tokens
func SeparateTokensByType(tokens []*MapProviderToken) (localTokens, onlineTokens []*MapProviderToken) {
	localTokens = make([]*MapProviderToken, 0)
	onlineTokens = make([]*MapProviderToken, 0)

	for _, token := range tokens {
		if token == nil {
			continue
		}

		if IsLocalDirectoryProvider(token.Provider) {
			localTokens = append(localTokens, token)
		} else {
			onlineTokens = append(onlineTokens, token)
		}
	}

	return localTokens, onlineTokens
}
