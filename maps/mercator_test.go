package maps

import (
	"math"
	"testing"
)

func TestNewMercator(t *testing.T) {
	tests := []struct {
		name     string
		tileSize int
	}{
		{
			name:     "Standard tile size 256",
			tileSize: 256,
		},
		{
			name:     "Large tile size 512",
			tileSize: 512,
		},
		{
			name:     "Small tile size 128",
			tileSize: 128,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := NewMercator(tt.tileSize)
			
			if m.tileSize != tt.tileSize {
				t.<PERSON>("NewMercator() tileSize = %v, want %v", m.tileSize, tt.tileSize)
			}
			
			expectedInitialResolution := math.Pi * 2.0 * 6378137.0 / float64(tt.tileSize)
			if math.Abs(m.initialResolution-expectedInitialResolution) > 0.001 {
				t.<PERSON><PERSON><PERSON>("NewMercator() initialResolution = %v, want %v", m.initialResolution, expectedInitialResolution)
			}
			
			expectedOriginShift := math.Pi * 2.0 * 6378137.0 / 2.0
			if math.Abs(m.originShift-expectedOriginShift) > 0.001 {
				t.<PERSON><PERSON><PERSON>("NewMercator() originShift = %v, want %v", m.originShift, expectedOriginShift)
			}
		})
	}
}

func TestMercator_LatLonToMeters(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name      string
		lat       float64
		lon       float64
		tolerance float64
	}{
		{
			name:      "Equator and Prime Meridian",
			lat:       0.0,
			lon:       0.0,
			tolerance: 1.0,
		},
		{
			name:      "Beijing",
			lat:       39.9042,
			lon:       116.4074,
			tolerance: 1000.0,
		},
		{
			name:      "Shanghai",
			lat:       31.2304,
			lon:       121.4737,
			tolerance: 1000.0,
		},
		{
			name:      "New York",
			lat:       40.7128,
			lon:       -74.0060,
			tolerance: 1000.0,
		},
		{
			name:      "London",
			lat:       51.5074,
			lon:       -0.1278,
			tolerance: 1000.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mx, my := m.LatLonToMeters(tt.lat, tt.lon)
			
			// Ensure valid meter coordinates
			if math.IsNaN(mx) || math.IsInf(mx, 0) {
				t.Errorf("LatLonToMeters() returned invalid mx: %v", mx)
			}
			if math.IsNaN(my) || math.IsInf(my, 0) {
				t.Errorf("LatLonToMeters() returned invalid my: %v", my)
			}
			
			// Meter coordinates should be within reasonable bounds
			maxBound := 20037508.342789244 // Web Mercator bounds
			if math.Abs(mx) > maxBound+1000 {
				t.Errorf("LatLonToMeters() mx out of bounds: %v", mx)
			}
			if math.Abs(my) > maxBound+1000 {
				t.Errorf("LatLonToMeters() my out of bounds: %v", my)
			}
		})
	}
}

func TestMercator_MetersToLatLon(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name      string
		mx        float64
		my        float64
		tolerance float64
	}{
		{
			name:      "Origin",
			mx:        0.0,
			my:        0.0,
			tolerance: 0.001,
		},
		{
			name:      "Positive coordinates",
			mx:        12958000.0, // Approximately Beijing longitude in meters
			my:        4865000.0,  // Approximately Beijing latitude in meters
			tolerance: 1.0,
		},
		{
			name:      "Negative coordinates",
			mx:        -8238000.0, // Approximately New York longitude in meters
			my:        4969000.0,  // Approximately New York latitude in meters
			tolerance: 1.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			lat, lon := m.MetersToLatLon(tt.mx, tt.my)
			
			// Ensure valid lat/lon coordinates
			if math.IsNaN(lat) || math.IsInf(lat, 0) {
				t.Errorf("MetersToLatLon() returned invalid lat: %v", lat)
			}
			if math.IsNaN(lon) || math.IsInf(lon, 0) {
				t.Errorf("MetersToLatLon() returned invalid lon: %v", lon)
			}
			
			// Lat/lon should be within valid bounds
			if lat < -85.0511 || lat > 85.0511 {
				t.Errorf("MetersToLatLon() lat out of bounds: %v", lat)
			}
			if lon < -180.0 || lon > 180.0 {
				t.Errorf("MetersToLatLon() lon out of bounds: %v", lon)
			}
		})
	}
}

func TestMercator_Resolution(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name string
		zoom int
	}{
		{
			name: "Zoom level 0",
			zoom: 0,
		},
		{
			name: "Zoom level 10",
			zoom: 10,
		},
		{
			name: "Zoom level 18",
			zoom: 18,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resolution := m.Resolution(tt.zoom)
			
			// Resolution should be positive
			if resolution <= 0 {
				t.Errorf("Resolution() = %v, want positive value", resolution)
			}
			
			// Higher zoom levels should have smaller resolution
			if tt.zoom > 0 {
				prevResolution := m.Resolution(tt.zoom - 1)
				if resolution >= prevResolution {
					t.Errorf("Resolution() at zoom %d should be smaller than zoom %d", tt.zoom, tt.zoom-1)
				}
			}
			
			// Resolution should be approximately initialResolution / 2^zoom
			expected := m.initialResolution / math.Pow(2, float64(tt.zoom))
			if math.Abs(resolution-expected) > 0.001 {
				t.Errorf("Resolution() = %v, want %v", resolution, expected)
			}
		})
	}
}

func TestMercator_MetersToPixels(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name string
		mx   float64
		my   float64
		zoom int
	}{
		{
			name: "Origin at zoom 0",
			mx:   0.0,
			my:   0.0,
			zoom: 0,
		},
		{
			name: "Beijing area at zoom 10",
			mx:   12958000.0,
			my:   4865000.0,
			zoom: 10,
		},
		{
			name: "High zoom level",
			mx:   1000000.0,
			my:   1000000.0,
			zoom: 18,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			px, py := m.MetersToPixels(tt.mx, tt.my, tt.zoom)
			
			// Ensure valid pixel coordinates
			if math.IsNaN(px) || math.IsInf(px, 0) {
				t.Errorf("MetersToPixels() returned invalid px: %v", px)
			}
			if math.IsNaN(py) || math.IsInf(py, 0) {
				t.Errorf("MetersToPixels() returned invalid py: %v", py)
			}
			
			// Pixel coordinates should be non-negative for positive meter coordinates
			if tt.mx >= 0 && tt.my >= 0 {
				if px < 0 || py < 0 {
					t.Errorf("MetersToPixels() returned negative pixels for positive meters: px=%v, py=%v", px, py)
				}
			}
		})
	}
}

func TestMercator_PixelsToMeters(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name string
		px   float64
		py   float64
		zoom int
	}{
		{
			name: "Origin at zoom 0",
			px:   128.0, // Center of tile at zoom 0
			py:   128.0,
			zoom: 0,
		},
		{
			name: "High zoom level",
			px:   65536.0,
			py:   65536.0,
			zoom: 10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mx, my := m.PixelsToMeters(tt.px, tt.py, tt.zoom)
			
			// Ensure valid meter coordinates
			if math.IsNaN(mx) || math.IsInf(mx, 0) {
				t.Errorf("PixelsToMeters() returned invalid mx: %v", mx)
			}
			if math.IsNaN(my) || math.IsInf(my, 0) {
				t.Errorf("PixelsToMeters() returned invalid my: %v", my)
			}
		})
	}
}

func TestMercator_LatLonToPixels(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name string
		lat  float64
		lon  float64
		zoom int
	}{
		{
			name: "Equator at zoom 0",
			lat:  0.0,
			lon:  0.0,
			zoom: 0,
		},
		{
			name: "Beijing at zoom 10",
			lat:  39.9042,
			lon:  116.4074,
			zoom: 10,
		},
		{
			name: "New York at zoom 15",
			lat:  40.7128,
			lon:  -74.0060,
			zoom: 15,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			px, py := m.LatLonToPixels(tt.lat, tt.lon, tt.zoom)
			
			// Ensure valid pixel coordinates
			if math.IsNaN(px) || math.IsInf(px, 0) {
				t.Errorf("LatLonToPixels() returned invalid px: %v", px)
			}
			if math.IsNaN(py) || math.IsInf(py, 0) {
				t.Errorf("LatLonToPixels() returned invalid py: %v", py)
			}
		})
	}
}

func TestMercator_PixelsToLatLon(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name string
		px   float64
		py   float64
		zoom int
	}{
		{
			name: "Center tile at zoom 0",
			px:   128.0,
			py:   128.0,
			zoom: 0,
		},
		{
			name: "High zoom level",
			px:   32768.0,
			py:   32768.0,
			zoom: 10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			lat, lon := m.PixelsToLatLon(tt.px, tt.py, tt.zoom)
			
			// Ensure valid lat/lon coordinates
			if math.IsNaN(lat) || math.IsInf(lat, 0) {
				t.Errorf("PixelsToLatLon() returned invalid lat: %v", lat)
			}
			if math.IsNaN(lon) || math.IsInf(lon, 0) {
				t.Errorf("PixelsToLatLon() returned invalid lon: %v", lon)
			}
			
			// Lat/lon should be within valid bounds
			if lat < -85.0511 || lat > 85.0511 {
				t.Errorf("PixelsToLatLon() lat out of bounds: %v", lat)
			}
			if lon < -180.0 || lon > 180.0 {
				t.Errorf("PixelsToLatLon() lon out of bounds: %v", lon)
			}
		})
	}
}

func TestMercator_PixelsToTiles(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name     string
		px       float64
		py       float64
		zoom     int
		expectedTx int
		expectedTy int
	}{
		{
			name:       "Origin tile",
			px:         0.0,
			py:         0.0,
			zoom:       0,
			expectedTx: 0,
			expectedTy: 0,
		},
		{
			name:       "Second tile horizontally",
			px:         256.0,
			py:         0.0,
			zoom:       1,
			expectedTx: 1,
			expectedTy: 0,
		},
		{
			name:       "Second tile vertically",
			px:         0.0,
			py:         256.0,
			zoom:       1,
			expectedTx: 0,
			expectedTy: 1,
		},
		{
			name:     "High zoom level",
			px:       65536.0,
			py:       32768.0,
			zoom:     10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx, ty := m.PixelsToTiles(tt.px, tt.py, tt.zoom)
			
			// Ensure valid tile coordinates
			if tx < 0 || ty < 0 {
				t.Errorf("PixelsToTiles() returned negative tile coordinates: tx=%v, ty=%v", tx, ty)
			}
			
			// Tile coordinates should be within bounds for the zoom level
			maxTileIndex := int(math.Pow(2, float64(tt.zoom))) - 1
			if tx > maxTileIndex || ty > maxTileIndex {
				t.Errorf("PixelsToTiles() tile coordinates out of bounds: tx=%v, ty=%v, max=%v", tx, ty, maxTileIndex)
			}
			
			// Check expected values if provided
			if tt.expectedTx >= 0 && tx != tt.expectedTx {
				t.Errorf("PixelsToTiles() tx = %v, want %v", tx, tt.expectedTx)
			}
			if tt.expectedTy >= 0 && ty != tt.expectedTy {
				t.Errorf("PixelsToTiles() ty = %v, want %v", ty, tt.expectedTy)
			}
		})
	}
}

func TestMercator_LatLonToTiles(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name string
		lat  float64
		lon  float64
		zoom int
	}{
		{
			name: "Equator at zoom 0",
			lat:  0.0,
			lon:  0.0,
			zoom: 0,
		},
		{
			name: "Beijing at zoom 10",
			lat:  39.9042,
			lon:  116.4074,
			zoom: 10,
		},
		{
			name: "New York at zoom 15",
			lat:  40.7128,
			lon:  -74.0060,
			zoom: 15,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx, ty := m.LatLonToTiles(tt.lat, tt.lon, tt.zoom)
			
			// Ensure valid tile coordinates
			if tx < 0 || ty < 0 {
				t.Errorf("LatLonToTiles() returned negative tile coordinates: tx=%v, ty=%v", tx, ty)
			}
			
			// Tile coordinates should be within bounds for the zoom level
			maxTileIndex := int(math.Pow(2, float64(tt.zoom))) - 1
			if tx > maxTileIndex || ty > maxTileIndex {
				t.Errorf("LatLonToTiles() tile coordinates out of bounds: tx=%v, ty=%v, max=%v", tx, ty, maxTileIndex)
			}
		})
	}
}

func TestMercator_GoogleTiles(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name       string
		gx         int
		gy         int
		gz         int
		expectedTx int
		expectedTy int
	}{
		{
			name:       "Zoom 0",
			gx:         0,
			gy:         0,
			gz:         0,
			expectedTx: 0,
			expectedTy: 0,
		},
		{
			name:       "Zoom 1 - top left",
			gx:         0,
			gy:         0,
			gz:         1,
			expectedTx: 0,
			expectedTy: 1,
		},
		{
			name:       "Zoom 1 - bottom right",
			gx:         1,
			gy:         1,
			gz:         1,
			expectedTx: 1,
			expectedTy: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tx, ty := m.GoogleTiles(tt.gx, tt.gy, tt.gz)
			
			if tx != tt.expectedTx {
				t.Errorf("GoogleTiles() tx = %v, want %v", tx, tt.expectedTx)
			}
			if ty != tt.expectedTy {
				t.Errorf("GoogleTiles() ty = %v, want %v", ty, tt.expectedTy)
			}
		})
	}
}

func TestMercator_LatLonToGoogleTiles(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name string
		lat  float64
		lon  float64
		zoom int
	}{
		{
			name: "Equator at zoom 0",
			lat:  0.0,
			lon:  0.0,
			zoom: 0,
		},
		{
			name: "Beijing at zoom 10",
			lat:  39.9042,
			lon:  116.4074,
			zoom: 10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gx, gy := m.LatLonToGoogleTiles(tt.lat, tt.lon, tt.zoom)
			
			// Ensure valid Google tile coordinates
			if gx < 0 || gy < 0 {
				t.Errorf("LatLonToGoogleTiles() returned negative coordinates: gx=%v, gy=%v", gx, gy)
			}
			
			// Google tile coordinates should be within bounds for the zoom level
			maxTileIndex := int(math.Pow(2, float64(tt.zoom))) - 1
			if gx > maxTileIndex || gy > maxTileIndex {
				t.Errorf("LatLonToGoogleTiles() coordinates out of bounds: gx=%v, gy=%v, max=%v", gx, gy, maxTileIndex)
			}
		})
	}
}

// Test coordinate conversion round-trip accuracy
func TestMercatorConversionRoundTrip(t *testing.T) {
	m := NewMercator(256)
	
	tests := []struct {
		name      string
		lat       float64
		lon       float64
		zoom      int
		tolerance float64
	}{
		{
			name:      "Beijing",
			lat:       39.9042,
			lon:       116.4074,
			zoom:      10,
			tolerance: 0.001,
		},
		{
			name:      "New York",
			lat:       40.7128,
			lon:       -74.0060,
			zoom:      15,
			tolerance: 0.001,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// LatLon -> Meters -> LatLon
			mx, my := m.LatLonToMeters(tt.lat, tt.lon)
			backLat, backLon := m.MetersToLatLon(mx, my)
			
			latDiff := math.Abs(backLat - tt.lat)
			lonDiff := math.Abs(backLon - tt.lon)
			
			if latDiff > tt.tolerance {
				t.Errorf("LatLon->Meters->LatLon lat difference too large: %v > %v", latDiff, tt.tolerance)
			}
			if lonDiff > tt.tolerance {
				t.Errorf("LatLon->Meters->LatLon lon difference too large: %v > %v", lonDiff, tt.tolerance)
			}
			
			// LatLon -> Pixels -> LatLon
			px, py := m.LatLonToPixels(tt.lat, tt.lon, tt.zoom)
			backLat2, backLon2 := m.PixelsToLatLon(px, py, tt.zoom)
			
			latDiff2 := math.Abs(backLat2 - tt.lat)
			lonDiff2 := math.Abs(backLon2 - tt.lon)
			
			if latDiff2 > tt.tolerance {
				t.Errorf("LatLon->Pixels->LatLon lat difference too large: %v > %v", latDiff2, tt.tolerance)
			}
			if lonDiff2 > tt.tolerance {
				t.Errorf("LatLon->Pixels->LatLon lon difference too large: %v > %v", lonDiff2, tt.tolerance)
			}
		})
	}
}
