package maps

import (
	"bfmap/dbproto"
	"os"
	"path/filepath"
	"testing"
)

// Integration tests for local directory fallback feature
// These tests simulate end-to-end scenarios without requiring database setup

// TestLocalDirectoryFallbackIntegration tests the complete fallback chain
func TestLocalDirectoryFallbackIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Create test directories and files
	testBaseDir := filepath.Join(os.TempDir(), "bfmap_integration_test")
	defer os.RemoveAll(testBaseDir)

	// Set up test directory structure
	googleDir := filepath.Join(testBaseDir, "google", "satellite", "16", "53393")
	err := os.MkdirAll(googleDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// Create test tile file
	testTileData := []byte("fake_satellite_tile_data_16_53393_28433")
	testTilePath := filepath.Join(googleDir, "28433.jpg")
	err = os.WriteFile(testTilePath, testTileData, 0644)
	if err != nil {
		t.Fatalf("Failed to create test tile: %v", err)
	}

	tests := []struct {
		name        string
		token       *MapProviderToken
		mapReq      *MapReq
		expectError bool
	}{
		{
			name: "successful local directory retrieval",
			token: &MapProviderToken{
				TokenRid: "test-local-token",
				Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
				BaseUrl:  filepath.Join(testBaseDir, "google"),
				Language: "zh-CN",
				Priority: 1,
				MinZoom:  1,
				MaxZoom:  20,
			},
			mapReq: &MapReq{
				Provider:    int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory),
				MapType:     "satellite",
				Z:           16,
				X:           53393,
				Y:           28433,
				Lang:        "zh-CN",
				ImageFormat: TileImageFormatJpg,
			},
			expectError: false,
		},
		{
			name: "file not found in local directory",
			token: &MapProviderToken{
				TokenRid: "test-local-token",
				Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
				BaseUrl:  filepath.Join(testBaseDir, "google"),
				Language: "zh-CN",
				Priority: 1,
				MinZoom:  1,
				MaxZoom:  20,
			},
			mapReq: &MapReq{
				Provider:    int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory),
				MapType:     "satellite",
				Z:           99, // Non-existent zoom level
				X:           99999,
				Y:           99999,
				Lang:        "zh-CN",
				ImageFormat: TileImageFormatJpg,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute ReadTileFromLocalDirectory directly
			tileInfo, imageBytes, err := ReadTileFromLocalDirectory(tt.token, tt.mapReq)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if tileInfo == nil {
				t.Errorf("Expected tileInfo but got nil")
			}
			if len(imageBytes) == 0 {
				t.Errorf("Expected imageBytes but got empty")
			}
			if len(imageBytes) > 0 && string(imageBytes) != string(testTileData) {
				t.Errorf("Image data mismatch: got %q, want %q", string(imageBytes), string(testTileData))
			}
		})
	}
}

// TestMultiLanguageIntegration tests language matching with actual file system
func TestMultiLanguageIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Create test directories for different languages
	testBaseDir := filepath.Join(os.TempDir(), "bfmap_multilang_test")
	defer os.RemoveAll(testBaseDir)

	// Set up directories for different languages
	// Note: Each language creates its own separate base directory
	// because local directories don't use language subdirectories
	languages := map[string]string{
		"zh-CN": "Chinese_tile_data",
		"en-US": "English_tile_data",
		"ja-JP": "Japanese_tile_data",
	}

	for lang, content := range languages {
		langBaseDir := filepath.Join(testBaseDir, lang)
		langDir := filepath.Join(langBaseDir, "google", "satellite", "10", "100")
		err := os.MkdirAll(langDir, 0755)
		if err != nil {
			t.Fatalf("Failed to create directory for %s: %v", lang, err)
		}

		// Create test file
		testPath := filepath.Join(langDir, "200.jpg")
		err = os.WriteFile(testPath, []byte(content), 0644)
		if err != nil {
			t.Fatalf("Failed to create test file for %s: %v", lang, err)
		}
	}

	tests := []struct {
		name          string
		requestLang   string
		tokenLang     string
		expectedData  string
		shouldSucceed bool
	}{
		{
			name:          "exact match zh-CN",
			requestLang:   "zh-CN",
			tokenLang:     "zh-CN",
			expectedData:  "Chinese_tile_data",
			shouldSucceed: true,
		},
		{
			name:          "exact match en-US",
			requestLang:   "en-US",
			tokenLang:     "en-US",
			expectedData:  "English_tile_data",
			shouldSucceed: true,
		},
		{
			name:          "language mismatch",
			requestLang:   "fr-FR",
			tokenLang:     "zh-CN",
			expectedData:  "Chinese_tile_data",
			shouldSucceed: true, // Should still work but with different language
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token := &MapProviderToken{
				TokenRid: "test-token",
				Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
				BaseUrl:  filepath.Join(testBaseDir, tt.tokenLang, "google"),
				Language: tt.tokenLang,
				Priority: 1,
				MinZoom:  1,
				MaxZoom:  20,
			}

			mapReq := &MapReq{
				Provider:    int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory),
				MapType:     "satellite",
				Z:           10,
				X:           100,
				Y:           200,
				Lang:        tt.requestLang,
				ImageFormat: TileImageFormatJpg,
			}

			tileInfo, imageBytes, err := ReadTileFromLocalDirectory(token, mapReq)

			if !tt.shouldSucceed {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if tileInfo == nil {
				t.Errorf("Expected tileInfo but got nil")
				return
			}

			if len(imageBytes) == 0 {
				t.Errorf("Expected imageBytes but got empty")
				return
			}

			if string(imageBytes) != tt.expectedData {
				t.Errorf("Data mismatch: got %q, want %q", string(imageBytes), tt.expectedData)
			}
		})
	}
}

// TestLanguageMatchingIntegration tests the complete language matching workflow
func TestLanguageMatchingIntegration(t *testing.T) {
	tests := []struct {
		name           string
		tokens         []*MapProviderToken
		requestLang    string
		expectedTokens int
		expectedFirst  string // Expected first token's language after sorting
	}{
		{
			name: "multiple tokens with language matching",
			tokens: []*MapProviderToken{
				{
					TokenRid: "token1",
					Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					Language: "en-US",
					Priority: 2,
				},
				{
					TokenRid: "token2",
					Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					Language: "zh-CN",
					Priority: 3,
				},
				{
					TokenRid: "token3",
					Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					Language: "ja-JP",
					Priority: 1,
				},
			},
			requestLang:    "zh-CN",
			expectedTokens: 3,
			expectedFirst:  "zh-CN", // Should be first due to exact language match
		},
		{
			name: "partial language match",
			tokens: []*MapProviderToken{
				{
					TokenRid: "token1",
					Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					Language: "en-US",
					Priority: 1,
				},
				{
					TokenRid: "token2",
					Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					Language: "zh-CN",
					Priority: 2,
				},
			},
			requestLang:    "zh", // Should match zh-CN
			expectedTokens: 2,
			expectedFirst:  "zh-CN", // Should be first due to language match
		},
		{
			name: "no language match - priority fallback",
			tokens: []*MapProviderToken{
				{
					TokenRid: "token1",
					Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					Language: "ja-JP",
					Priority: 2, // Lower priority value
				},
				{
					TokenRid: "token2",
					Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					Language: "ko-KR",
					Priority: 1, // Higher priority value
				},
			},
			requestLang:    "fr-FR",
			expectedTokens: 2,
			expectedFirst:  "ja-JP", // Should be first due to higher Priority value (2 > 1)
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test language matching
			matchResults, err := GetLanguageMatchedTokens(tt.tokens, tt.requestLang)
			if err != nil {
				t.Errorf("GetLanguageMatchedTokens failed: %v", err)
				return
			}

			if len(matchResults) != tt.expectedTokens {
				t.Errorf("Expected %d tokens, got %d", tt.expectedTokens, len(matchResults))
				return
			}

			// Test sorting
			sortedResults := SortTokensByConfidence(matchResults)
			if len(sortedResults) == 0 {
				t.Errorf("Expected sorted results but got empty")
				return
			}

			if sortedResults[0].Token.Language != tt.expectedFirst {
				t.Errorf("Expected first token language %s, got %s",
					tt.expectedFirst, sortedResults[0].Token.Language)
			}
		})
	}
}
