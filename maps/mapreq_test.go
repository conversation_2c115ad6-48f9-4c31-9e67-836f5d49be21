package maps

import (
	"testing"
)

func TestMapReq_IsRoadMap(t *testing.T) {
	tests := []struct {
		name    string
		mapType string
		want    bool
	}{
		{
			name:    "roadmap type",
			mapType: MapTypeRoadmap,
			want:    true,
		},
		{
			name:    "satellite type",
			mapType: MapTypeSatellite,
			want:    false,
		},
		{
			name:    "hybrid type",
			mapType: MapTypeHybrid,
			want:    false,
		},
		{
			name:    "empty type",
			mapType: "",
			want:    false,
		},
		{
			name:    "unknown type",
			mapType: "unknown",
			want:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &MapReq{
				MapType: tt.mapType,
			}
			if got := r.IsRoadMap(); got != tt.want {
				t.Errorf("MapReq.IsRoadMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMapReq_IsSatelliteHybrid(t *testing.T) {
	tests := []struct {
		name    string
		mapType string
		want    bool
	}{
		{
			name:    "satellite type",
			mapType: MapTypeSatellite,
			want:    true,
		},
		{
			name:    "hybrid type",
			mapType: MapTypeHybrid,
			want:    true,
		},
		{
			name:    "roadmap type",
			mapType: MapTypeRoadmap,
			want:    false,
		},
		{
			name:    "empty type",
			mapType: "",
			want:    false,
		},
		{
			name:    "unknown type",
			mapType: "unknown",
			want:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &MapReq{
				MapType: tt.mapType,
			}
			if got := r.IsSatelliteHybrid(); got != tt.want {
				t.Errorf("MapReq.IsSatelliteHybrid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMapReq_IsNeedTiandituMarkerForGoogleMap(t *testing.T) {
	tests := []struct {
		name         string
		mapType      string
		isOutOfChina bool
		enableGcj02  int
		want         bool
	}{
		{
			name:         "hybrid inside China",
			mapType:      MapTypeHybrid,
			isOutOfChina: false,
			enableGcj02:  0,
			want:         true,
		},
		{
			name:         "hybrid outside China with GCJ02 enabled",
			mapType:      MapTypeHybrid,
			isOutOfChina: true,
			enableGcj02:  1,
			want:         true,
		},
		{
			name:         "hybrid outside China without GCJ02",
			mapType:      MapTypeHybrid,
			isOutOfChina: true,
			enableGcj02:  0,
			want:         false,
		},
		{
			name:         "satellite inside China",
			mapType:      MapTypeSatellite,
			isOutOfChina: false,
			enableGcj02:  0,
			want:         false,
		},
		{
			name:         "roadmap inside China",
			mapType:      MapTypeRoadmap,
			isOutOfChina: false,
			enableGcj02:  0,
			want:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &MapReq{
				MapType:      tt.mapType,
				IsOutOfChina: tt.isOutOfChina,
				EnableGcj02:  tt.enableGcj02,
			}
			if got := r.IsNeedTiandituMarkerForGoogleMap(); got != tt.want {
				t.Errorf("MapReq.IsNeedTiandituMarkerForGoogleMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMapReq_Clone(t *testing.T) {
	original := &MapReq{
		X:              100,
		Y:              200,
		Z:              15,
		EnableGcj02:    1,
		WgsLon:         116.4074,
		WgsLat:         39.9042,
		GcjLon:         116.4074,
		GcjLat:         39.9042,
		MapType:        MapTypeSatellite,
		Lang:           "zh-CN",
		Provider:       1,
		ImageFormat:    TileImageFormatJpg,
		ImageFormatInt: 2,
		Region:         "CN",
	}

	cloned := original.Clone()

	// Test that all fields are copied correctly
	if cloned.X != original.X {
		t.Errorf("Clone() X = %v, want %v", cloned.X, original.X)
	}
	if cloned.Y != original.Y {
		t.Errorf("Clone() Y = %v, want %v", cloned.Y, original.Y)
	}
	if cloned.Z != original.Z {
		t.Errorf("Clone() Z = %v, want %v", cloned.Z, original.Z)
	}
	if cloned.EnableGcj02 != original.EnableGcj02 {
		t.Errorf("Clone() EnableGcj02 = %v, want %v", cloned.EnableGcj02, original.EnableGcj02)
	}
	if cloned.WgsLon != original.WgsLon {
		t.Errorf("Clone() WgsLon = %v, want %v", cloned.WgsLon, original.WgsLon)
	}
	if cloned.WgsLat != original.WgsLat {
		t.Errorf("Clone() WgsLat = %v, want %v", cloned.WgsLat, original.WgsLat)
	}
	if cloned.GcjLon != original.GcjLon {
		t.Errorf("Clone() GcjLon = %v, want %v", cloned.GcjLon, original.GcjLon)
	}
	if cloned.GcjLat != original.GcjLat {
		t.Errorf("Clone() GcjLat = %v, want %v", cloned.GcjLat, original.GcjLat)
	}
	if cloned.MapType != original.MapType {
		t.Errorf("Clone() MapType = %v, want %v", cloned.MapType, original.MapType)
	}
	if cloned.Lang != original.Lang {
		t.Errorf("Clone() Lang = %v, want %v", cloned.Lang, original.Lang)
	}
	if cloned.Provider != original.Provider {
		t.Errorf("Clone() Provider = %v, want %v", cloned.Provider, original.Provider)
	}
	if cloned.ImageFormat != original.ImageFormat {
		t.Errorf("Clone() ImageFormat = %v, want %v", cloned.ImageFormat, original.ImageFormat)
	}
	if cloned.ImageFormatInt != original.ImageFormatInt {
		t.Errorf("Clone() ImageFormatInt = %v, want %v", cloned.ImageFormatInt, original.ImageFormatInt)
	}
	if cloned.Region != original.Region {
		t.Errorf("Clone() Region = %v, want %v", cloned.Region, original.Region)
	}

	// Test that it's a different instance
	if cloned == original {
		t.Error("Clone() returned same instance, want different instance")
	}

	// Test that modifying clone doesn't affect original
	cloned.X = 999
	if original.X == 999 {
		t.Error("Modifying clone affected original")
	}
}

func TestMapReq_ParseImageFormat(t *testing.T) {
	tests := []struct {
		name               string
		imageFormat        string
		expectedFormatInt  int
	}{
		{
			name:              "PNG format",
			imageFormat:       TileImageFormatPng,
			expectedFormatInt: 1,
		},
		{
			name:              "JPG format",
			imageFormat:       TileImageFormatJpg,
			expectedFormatInt: 2,
		},
		{
			name:              "JPG baseline format",
			imageFormat:       TileImageFormatJpgBaseline,
			expectedFormatInt: 2,
		},
		{
			name:              "Empty format",
			imageFormat:       "",
			expectedFormatInt: 2,
		},
		{
			name:              "Unknown format",
			imageFormat:       "unknown",
			expectedFormatInt: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &MapReq{
				ImageFormat: tt.imageFormat,
			}
			r.ParseImageFormat()
			
			if r.ImageFormatInt != tt.expectedFormatInt {
				t.Errorf("ParseImageFormat() ImageFormatInt = %v, want %v", r.ImageFormatInt, tt.expectedFormatInt)
			}
		})
	}
}

func TestMapReq_KeyWithoutLangAndGcj01(t *testing.T) {
	tests := []struct {
		name     string
		mapReq   *MapReq
		expected string
	}{
		{
			name: "basic request",
			mapReq: &MapReq{
				X:        100,
				Y:        200,
				Z:        15,
				MapType:  MapTypeSatellite,
				Provider: 1,
			},
			expected: "100:200:15:satellite:1",
		},
		{
			name: "roadmap request",
			mapReq: &MapReq{
				X:        50,
				Y:        75,
				Z:        10,
				MapType:  MapTypeRoadmap,
				Provider: 2,
			},
			expected: "50:75:10:roadmap:2",
		},
		{
			name: "hybrid request",
			mapReq: &MapReq{
				X:        0,
				Y:        0,
				Z:        0,
				MapType:  MapTypeHybrid,
				Provider: 3,
			},
			expected: "0:0:0:hybrid:3",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.mapReq.KeyWithoutLangAndGcj01()
			if result != tt.expected {
				t.Errorf("KeyWithoutLangAndGcj01() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMapReq_Key(t *testing.T) {
	tests := []struct {
		name     string
		mapReq   *MapReq
		expected string
	}{
		{
			name: "complete request",
			mapReq: &MapReq{
				X:           100,
				Y:           200,
				Z:           15,
				MapType:     MapTypeSatellite,
				Provider:    1,
				Lang:        "zh-CN",
				EnableGcj02: 1,
			},
			expected: "100:200:15:satellite:1:zh-CN:1",
		},
		{
			name: "request without language",
			mapReq: &MapReq{
				X:           50,
				Y:           75,
				Z:           10,
				MapType:     MapTypeRoadmap,
				Provider:    2,
				Lang:        "",
				EnableGcj02: 0,
			},
			expected: "50:75:10:roadmap:2::0",
		},
		{
			name: "request with English language",
			mapReq: &MapReq{
				X:           25,
				Y:           30,
				Z:           8,
				MapType:     MapTypeHybrid,
				Provider:    3,
				Lang:        "en-US",
				EnableGcj02: 0,
			},
			expected: "25:30:8:hybrid:3:en-US:0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.mapReq.Key()
			if result != tt.expected {
				t.Errorf("Key() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMapReq_CacheKey(t *testing.T) {
	tests := []struct {
		name     string
		mapReq   *MapReq
		ext      string
		expected string
	}{
		{
			name: "cache key with jpg extension",
			mapReq: &MapReq{
				X:           100,
				Y:           200,
				Z:           15,
				MapType:     MapTypeSatellite,
				Provider:    1,
				Lang:        "zh-CN",
				EnableGcj02: 1,
			},
			ext:      "jpg",
			expected: "100:200:15:satellite:1:zh-CN:1-jpg",
		},
		{
			name: "cache key with png extension",
			mapReq: &MapReq{
				X:           50,
				Y:           75,
				Z:           10,
				MapType:     MapTypeRoadmap,
				Provider:    2,
				Lang:        "en-US",
				EnableGcj02: 0,
			},
			ext:      "png",
			expected: "50:75:10:roadmap:2:en-US:0-png",
		},
		{
			name: "cache key with custom extension",
			mapReq: &MapReq{
				X:           25,
				Y:           30,
				Z:           8,
				MapType:     MapTypeHybrid,
				Provider:    3,
				Lang:        "",
				EnableGcj02: 1,
			},
			ext:      "webp",
			expected: "25:30:8:hybrid:3::1-webp",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.mapReq.CacheKey(tt.ext)
			if result != tt.expected {
				t.Errorf("CacheKey() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMapReq_BlockKey(t *testing.T) {
	mapReq := &MapReq{
		X:           100,
		Y:           200,
		Z:           15,
		MapType:     MapTypeSatellite,
		Provider:    1,
		Lang:        "zh-CN",
		EnableGcj02: 1,
	}

	expected := "100:200:15:satellite:1:zh-CN:1-block"
	result := mapReq.BlockKey()

	if result != expected {
		t.Errorf("BlockKey() = %v, want %v", result, expected)
	}
}

// Test edge cases and boundary conditions
func TestMapReq_EdgeCases(t *testing.T) {
	t.Run("zero values", func(t *testing.T) {
		r := &MapReq{}
		
		// Test methods with zero values
		if r.IsRoadMap() {
			t.Error("IsRoadMap() should return false for empty MapType")
		}
		
		if r.IsSatelliteHybrid() {
			t.Error("IsSatelliteHybrid() should return false for empty MapType")
		}
		
		if r.IsNeedTiandituMarkerForGoogleMap() {
			t.Error("IsNeedTiandituMarkerForGoogleMap() should return false for empty MapType")
		}
		
		// Test key generation with zero values
		key := r.Key()
		expected := "0:0:0:::0"
		if key != expected {
			t.Errorf("Key() with zero values = %v, want %v", key, expected)
		}
	})

	t.Run("negative coordinates", func(t *testing.T) {
		r := &MapReq{
			X:        -100,
			Y:        -200,
			Z:        -5,
			MapType:  MapTypeSatellite,
			Provider: 1,
		}
		
		key := r.KeyWithoutLangAndGcj01()
		expected := "-100:-200:-5:satellite:1"
		if key != expected {
			t.Errorf("KeyWithoutLangAndGcj01() with negative coords = %v, want %v", key, expected)
		}
	})

	t.Run("large coordinates", func(t *testing.T) {
		r := &MapReq{
			X:        999999,
			Y:        888888,
			Z:        20,
			MapType:  MapTypeHybrid,
			Provider: 999,
		}
		
		key := r.KeyWithoutLangAndGcj01()
		expected := "999999:888888:20:hybrid:999"
		if key != expected {
			t.Errorf("KeyWithoutLangAndGcj01() with large coords = %v, want %v", key, expected)
		}
	})
}
