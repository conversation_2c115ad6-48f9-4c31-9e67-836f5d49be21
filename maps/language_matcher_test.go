package maps

import (
	"bfmap/dbproto"
	"testing"

	"golang.org/x/text/language"
)

func TestGetLanguageMatchedTokens(t *testing.T) {
	// Create test tokens
	tokens := []*MapProviderToken{
		{
			TokenRid: "token1",
			Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			Language: "zh-CN",
			Priority: 1,
		},
		{
			TokenRid: "token2",
			Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			Language: "en-US",
			Priority: 2,
		},
		{
			TokenRid: "token3",
			Provider: dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
			Language: "zh-CN",
			Priority: 3,
		},
	}

	tests := []struct {
		name          string
		tokens        []*MapProviderToken
		requestedLang string
		wantLen       int
		wantErr       bool
	}{
		{
			name:          "valid tokens with zh-CN request",
			tokens:        tokens,
			requestedLang: "zh-CN",
			wantLen:       3,
			wantErr:       false,
		},
		{
			name:          "valid tokens with empty request",
			tokens:        tokens,
			requestedLang: "",
			wantLen:       3,
			wantErr:       false,
		},
		{
			name:          "empty tokens",
			tokens:        []*MapProviderToken{},
			requestedLang: "zh-CN",
			wantLen:       0,
			wantErr:       true,
		},
		{
			name:          "nil tokens",
			tokens:        nil,
			requestedLang: "zh-CN",
			wantLen:       0,
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := GetLanguageMatchedTokens(tt.tokens, tt.requestedLang)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetLanguageMatchedTokens() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if len(results) != tt.wantLen {
				t.Errorf("GetLanguageMatchedTokens() len = %v, want %v", len(results), tt.wantLen)
				return
			}

			// Verify all results have valid tokens and confidence
			for _, result := range results {
				if result.Token == nil {
					t.Error("GetLanguageMatchedTokens() result has nil token")
				}
			}
		})
	}
}

func TestMatchGoogleLocalDirectoryLanguage(t *testing.T) {
	tests := []struct {
		name          string
		tokenLang     string
		requestedLang string
		wantConf      language.Confidence
	}{
		{
			name:          "exact match zh-CN",
			tokenLang:     "zh-CN",
			requestedLang: "zh-CN",
			wantConf:      language.Exact,
		},
		{
			name:          "high match zh to zh-CN",
			tokenLang:     "zh-CN",
			requestedLang: "zh",
			wantConf:      language.Exact, // language.NewMatcher treats this as exact match
		},
		{
			name:          "exact match en-US",
			tokenLang:     "en-US",
			requestedLang: "en-US",
			wantConf:      language.Exact,
		},
		{
			name:          "high match en to en-US",
			tokenLang:     "en-US",
			requestedLang: "en",
			wantConf:      language.Exact, // language.NewMatcher treats this as exact match
		},
		{
			name:          "no match different languages",
			tokenLang:     "en-US",
			requestedLang: "zh-CN",
			wantConf:      language.No,
		},
		{
			name:          "empty token language",
			tokenLang:     "",
			requestedLang: "zh-CN",
			wantConf:      language.High,
		},
		{
			name:          "empty requested language",
			tokenLang:     "zh-CN",
			requestedLang: "",
			wantConf:      language.High,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token := &MapProviderToken{
				Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
				Language: tt.tokenLang,
			}

			requestedTag := language.Und
			if tt.requestedLang != "" {
				var err error
				requestedTag, err = language.Parse(tt.requestedLang)
				if err != nil {
					t.Fatalf("Failed to parse requested language: %v", err)
				}
			}

			conf := matchGoogleLocalDirectoryLanguage(token, requestedTag)

			if conf != tt.wantConf {
				t.Errorf("matchGoogleLocalDirectoryLanguage() = %v, want %v", conf, tt.wantConf)
			}
		})
	}
}

func TestMatchLocalDirectoryLanguage(t *testing.T) {
	tests := []struct {
		name          string
		provider      dbproto.MapProviderEnum
		tokenLang     string
		requestedLang string
		wantConf      language.Confidence
	}{
		{
			name:          "OSM local directory always high",
			provider:      dbproto.MapProviderEnum_ProviderOSMLocalDirectory,
			tokenLang:     "",
			requestedLang: "zh-CN",
			wantConf:      language.High,
		},
		{
			name:          "Tianditu exact match zh-CN",
			provider:      dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
			tokenLang:     "zh-CN",
			requestedLang: "zh-CN",
			wantConf:      language.Exact,
		},
		{
			name:          "Tianditu high match zh",
			provider:      dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
			tokenLang:     "zh-CN",
			requestedLang: "zh",
			wantConf:      language.High,
		},
		{
			name:          "Tianditu low match en",
			provider:      dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
			tokenLang:     "zh-CN",
			requestedLang: "en",
			wantConf:      language.Low,
		},
		{
			name:          "Google exact match",
			provider:      dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			tokenLang:     "en-US",
			requestedLang: "en-US",
			wantConf:      language.Exact,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token := &MapProviderToken{
				Provider: tt.provider,
				Language: tt.tokenLang,
			}

			requestedTag := language.Und
			if tt.requestedLang != "" {
				var err error
				requestedTag, err = language.Parse(tt.requestedLang)
				if err != nil {
					t.Fatalf("Failed to parse requested language: %v", err)
				}
			}

			conf := matchLocalDirectoryLanguage(token, requestedTag)

			if conf != tt.wantConf {
				t.Errorf("matchLocalDirectoryLanguage() = %v, want %v", conf, tt.wantConf)
			}
		})
	}
}

func TestSortTokensByConfidence(t *testing.T) {
	// Create test tokens with different confidence and priority combinations
	results := []LanguageMatchResult{
		{
			Token: &MapProviderToken{
				TokenRid: "token1",
				Priority: 1,
			},
			Confidence: language.Low,
		},
		{
			Token: &MapProviderToken{
				TokenRid: "token2",
				Priority: 3,
			},
			Confidence: language.Exact,
		},
		{
			Token: &MapProviderToken{
				TokenRid: "token3",
				Priority: 2,
			},
			Confidence: language.High,
		},
		{
			Token: &MapProviderToken{
				TokenRid: "token4",
				Priority: 5,
			},
			Confidence: language.High,
		},
	}

	sorted := SortTokensByConfidence(results)

	// Expected order: token2 (Exact, P3), token4 (High, P5), token3 (High, P2), token1 (Low, P1)
	expectedOrder := []string{"token2", "token4", "token3", "token1"}

	if len(sorted) != len(expectedOrder) {
		t.Errorf("SortTokensByConfidence() len = %v, want %v", len(sorted), len(expectedOrder))
		return
	}

	for i, expected := range expectedOrder {
		if sorted[i].Token.TokenRid != expected {
			t.Errorf("SortTokensByConfidence() position %d = %v, want %v", i, sorted[i].Token.TokenRid, expected)
		}
	}

	// Verify original slice is not modified
	if results[0].Token.TokenRid == sorted[0].Token.TokenRid {
		// This test might fail if the first element happens to be the same,
		// but it's unlikely given our test data
		if len(results) > 1 && results[1].Token.TokenRid == sorted[1].Token.TokenRid {
			t.Error("SortTokensByConfidence() modified original slice")
		}
	}
}

func TestConfidenceValue(t *testing.T) {
	tests := []struct {
		name       string
		confidence language.Confidence
		want       int
	}{
		{"Exact", language.Exact, 3},
		{"High", language.High, 2},
		{"Low", language.Low, 1},
		{"No", language.No, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := int(tt.confidence); got != tt.want {
				t.Errorf("confidence int value = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidateAndNormalizeLanguage(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
		wantErr  bool
	}{
		{
			name:     "empty string",
			input:    "",
			expected: "",
			wantErr:  true,
		},
		{
			name:     "valid BCP47 tag - zh-CN",
			input:    "zh-CN",
			expected: "zh-CN",
			wantErr:  false,
		},
		{
			name:     "valid BCP47 tag - en-US",
			input:    "en-US",
			expected: "en-US",
			wantErr:  false,
		},
		{
			name:     "simple language code - zh",
			input:    "zh",
			expected: "zh",
			wantErr:  false,
		},
		{
			name:     "simple language code - en",
			input:    "en",
			expected: "en",
			wantErr:  false,
		},
		{
			name:     "case normalization - ZH-cn",
			input:    "ZH-cn",
			expected: "zh-CN",
			wantErr:  false,
		},
		{
			name:     "invalid language tag",
			input:    "invalid-tag-123",
			expected: "",
			wantErr:  true,
		},
		{
			name:     "special characters",
			input:    "zh@#$%",
			expected: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ValidateAndNormalizeLanguage(tt.input)

			if tt.wantErr {
				if err == nil {
					t.Errorf("ValidateAndNormalizeLanguage() expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("ValidateAndNormalizeLanguage() unexpected error: %v", err)
				}
				if result != tt.expected {
					t.Errorf("ValidateAndNormalizeLanguage() = %v, expected %v", result, tt.expected)
				}
			}
		})
	}
}

func TestSeparateTokensByType(t *testing.T) {
	tokens := []*MapProviderToken{
		{
			TokenRid: "local1",
			Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
		},
		{
			TokenRid: "online1",
			Provider: dbproto.MapProviderEnum_ProviderGoogle,
		},
		{
			TokenRid: "local2",
			Provider: dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
		},
		{
			TokenRid: "online2",
			Provider: dbproto.MapProviderEnum_ProviderTianditu,
		},
		{
			TokenRid: "local3",
			Provider: dbproto.MapProviderEnum_ProviderOSMLocalDirectory,
		},
		nil, // Test nil token handling
	}

	localTokens, onlineTokens := SeparateTokensByType(tokens)

	// Check local tokens
	expectedLocalCount := 3
	if len(localTokens) != expectedLocalCount {
		t.Errorf("Expected %d local tokens, got %d", expectedLocalCount, len(localTokens))
	}

	expectedLocalProviders := map[string]dbproto.MapProviderEnum{
		"local1": dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
		"local2": dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
		"local3": dbproto.MapProviderEnum_ProviderOSMLocalDirectory,
	}

	for _, token := range localTokens {
		expectedProvider, exists := expectedLocalProviders[token.TokenRid]
		if !exists {
			t.Errorf("Unexpected local token: %s", token.TokenRid)
		}
		if token.Provider != expectedProvider {
			t.Errorf("Token %s has provider %v, expected %v", token.TokenRid, token.Provider, expectedProvider)
		}
	}

	// Check online tokens
	expectedOnlineCount := 2
	if len(onlineTokens) != expectedOnlineCount {
		t.Errorf("Expected %d online tokens, got %d", expectedOnlineCount, len(onlineTokens))
	}

	expectedOnlineProviders := map[string]dbproto.MapProviderEnum{
		"online1": dbproto.MapProviderEnum_ProviderGoogle,
		"online2": dbproto.MapProviderEnum_ProviderTianditu,
	}

	for _, token := range onlineTokens {
		expectedProvider, exists := expectedOnlineProviders[token.TokenRid]
		if !exists {
			t.Errorf("Unexpected online token: %s", token.TokenRid)
		}
		if token.Provider != expectedProvider {
			t.Errorf("Token %s has provider %v, expected %v", token.TokenRid, token.Provider, expectedProvider)
		}
	}
}

func TestSeparateTokensByType_EmptySlice(t *testing.T) {
	localTokens, onlineTokens := SeparateTokensByType([]*MapProviderToken{})

	if len(localTokens) != 0 {
		t.Errorf("Expected 0 local tokens, got %d", len(localTokens))
	}

	if len(onlineTokens) != 0 {
		t.Errorf("Expected 0 online tokens, got %d", len(onlineTokens))
	}
}

func TestSeparateTokensByType_NilSlice(t *testing.T) {
	localTokens, onlineTokens := SeparateTokensByType(nil)

	if len(localTokens) != 0 {
		t.Errorf("Expected 0 local tokens, got %d", len(localTokens))
	}

	if len(onlineTokens) != 0 {
		t.Errorf("Expected 0 online tokens, got %d", len(onlineTokens))
	}
}

func TestLanguageMatchResult_String(t *testing.T) {
	tests := []struct {
		name     string
		result   LanguageMatchResult
		expected string
	}{
		{
			name: "valid token",
			result: LanguageMatchResult{
				Token: &MapProviderToken{
					TokenRid: "test-rid",
					Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					Language: "zh-CN",
				},
				Confidence: language.Exact,
			},
			expected: "LanguageMatchResult{TokenRid: test-rid, Provider: ProviderGoogleLocalDirectory, Language: zh-CN, Confidence: Exact}",
		},
		{
			name: "nil token",
			result: LanguageMatchResult{
				Token:      nil,
				Confidence: language.No,
			},
			expected: "LanguageMatchResult{Token: nil, Confidence: No}",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.result.String()
			if result != tt.expected {
				t.Errorf("String() = %v, want %v", result, tt.expected)
			}
		})
	}
}
