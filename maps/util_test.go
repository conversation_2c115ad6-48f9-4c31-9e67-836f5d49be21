package maps

import (
	"bfmap/dbproto"
	"testing"
)

func TestIsLocalDirectoryProvider(t *testing.T) {
	tests := []struct {
		name     string
		provider dbproto.MapProviderEnum
		want     bool
	}{
		{
			name:     "ProviderGoogleLocalDirectory",
			provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			want:     true,
		},
		{
			name:     "ProviderTiandituLocalDirectory",
			provider: dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
			want:     true,
		},
		{
			name:     "ProviderOSMLocalDirectory",
			provider: dbproto.MapProviderEnum_ProviderOSMLocalDirectory,
			want:     true,
		},
		{
			name:     "ProviderGoogle",
			provider: dbproto.MapProviderEnum_ProviderGoogle,
			want:     false,
		},
		{
			name:     "ProviderTianditu",
			provider: dbproto.MapProviderEnum_ProviderTianditu,
			want:     false,
		},
		{
			name:     "ProviderOSM",
			provider: dbproto.MapProviderEnum_ProviderOSM,
			want:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsLocalDirectoryProvider(tt.provider); got != tt.want {
				t.Errorf("IsLocalDirectoryProvider() = %v, want %v", got, tt.want)
			}
		})
	}
}
