package maps

import (
	"bfmap/dbproto"
	"testing"
)

func TestIsLocalDirectoryProvider(t *testing.T) {
	tests := []struct {
		name     string
		provider dbproto.MapProviderEnum
		want     bool
	}{
		{
			name:     "ProviderGoogleLocalDirectory",
			provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			want:     true,
		},
		{
			name:     "ProviderTiandituLocalDirectory",
			provider: dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
			want:     true,
		},
		{
			name:     "ProviderOSMLocalDirectory",
			provider: dbproto.MapProviderEnum_ProviderOSMLocalDirectory,
			want:     true,
		},
		{
			name:     "ProviderGoogle",
			provider: dbproto.MapProviderEnum_ProviderGoogle,
			want:     false,
		},
		{
			name:     "ProviderTianditu",
			provider: dbproto.MapProviderEnum_ProviderTianditu,
			want:     false,
		},
		{
			name:     "ProviderOSM",
			provider: dbproto.MapProviderEnum_ProviderOSM,
			want:     false,
		},
		{
			name:     "Unknown provider",
			provider: dbproto.MapProviderEnum(999),
			want:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsLocalDirectoryProvider(tt.provider); got != tt.want {
				t.Errorf("IsLocalDirectoryProvider() = %v, want %v", got, tt.want)
			}
		})
	}
}

// Test additional utility functions that might exist in util.go
func TestUtilityFunctions(t *testing.T) {
	// This is a placeholder for testing other utility functions
	// that might be defined in util.go but not currently tested

	t.Run("placeholder for additional utility tests", func(t *testing.T) {
		// Add tests for other utility functions as needed
		// For example: file path utilities, string helpers, etc.
	})
}
