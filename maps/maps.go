package maps

import (
	"bfmap/bfnats"
	"bfmap/bfutil"
	"bfmap/config"
	"bfmap/db"
	"bfmap/dbproto"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/ygrpc/protodb/crud"
	"github.com/zeebo/xxh3"
	"golang.org/x/text/language"
)

const WeekInSeconds = 60 * 60 * 24 * 7

// map req block result, use in single flight request as result
type mapReqBlockResult struct {
	ImageBytes []byte
	TileInfo   *TileInfo
}

// request format: /map?token=xxxxxxx&x=123&y=456&z=7&mtype=roadmap&lang=en&provider=google&gcj02=1&sysname=trial&sysexpired=0
func MapHttpHandler(w http.ResponseWriter, r *http.Request) {
	if config.IsVerboseDebugMap {
		slog.Debug("got map request", "url", r.URL.String())
	}

	// get map request token
	token := r.URL.Query().Get("token")
	if token == "" {
		respondHttpError(w, r, "no token", http.StatusBadRequest)
		return
	}

	if IsTempMapProjectToken(token) {
		resolveTempMapProjectToken(w, r)
		return
	}

	sysName := r.URL.Query().Get("sysname")
	if sysName == "" {
		respondHttpError(w, r, "no sys name", http.StatusBadRequest)
		return
	}

	// validate token
	project, projectToken, status, err := validateMapReqToken(token, sysName)
	if err != nil {
		respondHttpError(w, r, err.Error(), status)
		return
	}
	go projectToken.AddOneUsageCount()

	mapReq, status, err := parseMapReq(r, projectToken)
	if err != nil {
		respondHttpError(w, r, err.Error(), status)
		return
	}

	if r.URL.Query().Get("sysexpired") == "1" {
		handleWithCacheOrOSMMapTileWithOptions(w, r, mapReq, true)
		return
	}

	// block map req processing, wait for result(image bytes)
	result, err, _ := GlobalMapsManager.sfg.Do(mapReq.BlockKey(), func() (any, error) {
		tileInfo, imageBytes, err := QueryTileFromKeyValueDb(mapReq)
		if config.IsVerboseDebugMap {
			slog.Debug(
				"query tile from key-value db",
				"mapReq key",
				mapReq.Key(),
				"len",
				len(imageBytes),
				"err",
				err,
			)
		}
		needUpdateTile := false
		if err == nil {
			// check if tile need to be updated
			cacheTime := time.Unix(tileInfo.CacheTime, 0)

			if (time.Since(cacheTime) > 5*time.Hour*24*365 && mapReq.Z > 9) ||
				tileInfo.Status == 8 {
				needUpdateTile = true
			}
			if !needUpdateTile {
				return &mapReqBlockResult{ImageBytes: imageBytes, TileInfo: tileInfo}, nil
			}
		}

		// it could be not found in db or tile need to be updated
		// Try local directory fallback first before NATS and online services
		if !needUpdateTile {
			tileInfo, imageBytes, err = QueryTileFromLocalDirectory(project.UserRid, mapReq)
			if err == nil {
				if config.IsVerboseDebugMap {
					slog.Debug("Local directory fallback succeeded",
						"mapReq", mapReq.Key(),
						"imageSize", len(imageBytes))
				}
				// Save to cache and return
				savedTileInfo, saveErr := SaveTileToDb(mapReq, imageBytes, false)
				if saveErr != nil {
					slog.Warn("save local directory tile to db fail", "mapReq", mapReq.Key(), "err", saveErr)
				} else {
					tileInfo = savedTileInfo
				}
				return &mapReqBlockResult{ImageBytes: imageBytes, TileInfo: tileInfo}, nil
			}

			if config.IsVerboseDebugMap {
				slog.Debug("Local directory fallback failed, continuing with existing flow",
					"mapReq", mapReq.Key(),
					"error", err.Error())
			}
		}

		if !needUpdateTile && config.NatsRequestMapTile {
			// not found in db, query tile from nats
			mapReqBytes, err := json.Marshal(mapReq)
			if err == nil {
				msg, err := bfnats.NatsRequest(
					mapReqNatSubject,
					mapReqBytes,
					time.Duration(config.NatsWaitTimeout)*time.Second,
				)
				if config.IsVerboseDebugMap {
					slog.Debug("send map req to nats", "msg", msg, "err", err)
				}
				if err == nil {
					tileInfo, err = SaveTileToDb(mapReq, msg.Data, false)
					if err != nil {
						slog.Warn("got tile from nats but save to db fail", "err", err)
						return &mapReqBlockResult{
							ImageBytes: msg.Data,
						}, nil
					}
					return &mapReqBlockResult{
						ImageBytes: msg.Data,
						TileInfo:   tileInfo,
					}, nil
				}
			}
		}

		// query tile from tile server
		tileInfo, imageBytes, err = QueryTileFromProvider(project.UserRid, mapReq, !needUpdateTile)
		if err != nil {
			return nil, err
		}

		return &mapReqBlockResult{ImageBytes: imageBytes, TileInfo: tileInfo}, err
	})

	if err != nil {
		respondHttpError(w, r, err.Error(), http.StatusInternalServerError)
		return
	}

	mapReqBlockResult, ok := result.(*mapReqBlockResult)
	if !ok {
		respondHttpError(w, r, "result is not *mapReqBlockResult in http handler", http.StatusInternalServerError)
		return
	}

	// respond image bytes
	respondImageBytes(w, r, mapReq, mapReqBlockResult.TileInfo, mapReqBlockResult.ImageBytes)
	// store map req access time
	GlobalMapsManager.UpdateMapCacheIndexAccessTime(mapReq)
}

func resolveTempMapProjectToken(w http.ResponseWriter, r *http.Request) {
	mapReq, status, err := parseMapReq(r, nil)
	if err != nil {
		respondHttpError(w, r, err.Error(), status)
		return
	}

	handleWithCacheOrOSMMapTileWithOptions(w, r, mapReq, false)
}

func handleWithCacheOrOSMMapTileWithOptions(
	w http.ResponseWriter,
	r *http.Request,
	mapReq *MapReq,
	enableLocalDirectory bool,
) {
	var imageBytes []byte
	var tileInfo *TileInfo
	var err error
	defer func() {
		if imageBytes != nil && err == nil {
			respondImageBytes(w, r, mapReq, tileInfo, imageBytes)
			return
		}

		if mapReq.Provider == int(dbproto.MapProviderEnum_ProviderOSM) &&
			mapReq.MapType == MapTypeRoadmap {
			b, err2 := ReqMapTileFromDefaultOSM(mapReq.X, mapReq.Y, mapReq.Z)
			if err2 == nil {
				respondImageBytes(w, r, mapReq, nil, b)
				return
			}

			respondHttpError(
				w,
				r,
				"failed to request map tile from osm server: "+err2.Error(),
				http.StatusInternalServerError,
			)
			return
		}

		respondHttpError(w, r, err.Error(), http.StatusNotFound)
	}()

	tileInfo, imageBytes, err = QueryTileFromKeyValueDb(mapReq)
	if err == nil {
		return
	}

	// 根据enableLocalDirectory参数决定是否尝试本地目录查询
	if enableLocalDirectory {
		// 系统过期场景：尝试本地目录查询
		// 使用特殊的userRid用于系统级别的本地目录tokens
		tileInfo, imageBytes, err = QueryTileFromLocalDirectory("system-expired", mapReq)
		if err == nil {
			// 成功获取到本地目录瓦片，保存到缓存
			savedTileInfo, saveErr := SaveTileToDb(mapReq, imageBytes, true)
			if saveErr == nil {
				tileInfo = savedTileInfo
			}
			// 通过defer函数返回
			return
		}
	}
}

func validateMapReqToken(
	token, sysName string,
) (project *MapProject, projectToken *MapProjectToken, status int, err error) {
	// before everything else, check token and add one usage count first
	t, err := GlobalMapsManager.GetMapProjectTokenCache(token)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil, http.StatusNotFound, errors.New("token not found")
		}
		return nil, nil, http.StatusNotFound, fmt.Errorf("get project token failed: %w", err)
	}
	if !t.IsActive() {
		return nil, nil, http.StatusForbidden, errors.New("token not active")
	}
	if t.IsExpired() {
		return nil, nil, http.StatusForbidden, errors.New("token expired")
	}

	if !t.HasSysName(sysName) {
		return nil, nil, http.StatusForbidden, errors.New("sys name not match")
	}

	p, err := GlobalMapsManager.GetMapProjectCache(t.ProjectRid)
	if err != nil {
		return nil, nil, http.StatusNotFound, fmt.Errorf("get project failed: %w", err)
	}
	if !p.IsActive() {
		return nil, nil, http.StatusForbidden, errors.New("project not active")
	}

	if p.IsQuotasOver() {
		return nil, nil, http.StatusForbidden, errors.New("project quotas over")
	}

	return p, t, http.StatusOK, nil
}

func IsMapTypeValid(mapType string) bool {
	switch mapType {
	case MapTypeRoadmap:
		return true
	case MapTypeSatellite:
		return true
	case MapTypeHybrid:
		return true
	}

	return false
}

func parseMapReq(r *http.Request, projectToken *MapProjectToken) (mapReq *MapReq, status int, err error) {
	xInt, yInt, zInt, err := parseXYZ(r)
	if err != nil {
		return nil, http.StatusBadRequest, err
	}

	mtype := r.URL.Query().Get("mtype")
	if len(mtype) == 0 {
		mtype = MapTypeRoadmap
	}

	if !IsMapTypeValid(mtype) {
		return nil, http.StatusForbidden, errors.New("invalid map type")
	}

	provider := parseProvider(r)

	if projectToken != nil {
		if provider < 0 {
			provider = projectToken.DefaultProvider(mtype)
		}

		if !projectToken.IsMapProviderAllowed(provider) {
			return nil, http.StatusForbidden, errors.New("provider not allowed")
		}
	}

	if provider == int(dbproto.MapProviderEnum_ProviderOSM) &&
		mtype != MapTypeRoadmap {
		return nil, http.StatusNotAcceptable, errors.New("OSM map type should be roadmap")
	}

	gcj02Str := r.URL.Query().Get("gcj02")
	gcj02, _ := strconv.Atoi(gcj02Str)
	if provider != int(dbproto.MapProviderEnum_ProviderGoogle) {
		gcj02 = 0
	}

	imageFormat := TileImageFormatPng
	if mtype == MapTypeSatellite || mtype == MapTypeHybrid {
		imageFormat = TileImageFormatJpg
	}

	if provider == int(dbproto.MapProviderEnum_ProviderOSM) {
		imageFormat = TileImageFormatPng
	}

	lang := r.URL.Query().Get("lang")
	langTag, err := language.All.Parse(lang)
	if err != nil {
		return nil, 0, fmt.Errorf("invalid language: %w", err)
	}
	lang = langTag.String()

	region := "US"
	langRegion, _ := langTag.Region()
	regionStr := langRegion.String()
	if regionStr != "ZZ" {
		region = regionStr
	}

	if len(lang) == 0 {
		lang = "en"
	}

	if provider == int(dbproto.MapProviderEnum_ProviderOSM) || mtype == MapTypeSatellite {
		lang = ""
	}

	if provider == int(dbproto.MapProviderEnum_ProviderTianditu) {
		lang = "zh-CN"
	}

	mapReq = &MapReq{
		X:           xInt,
		Y:           yInt,
		Z:           zInt,
		EnableGcj02: gcj02,
		MapType:     mtype,
		Lang:        lang,
		Provider:    provider,
		ImageFormat: imageFormat,
		Region:      region,
	}
	mapReq.CalcParams()
	return mapReq, http.StatusOK, nil
}

func parseXYZ(r *http.Request) (xInt int, yInt int, zInt int, err error) {
	x := r.URL.Query().Get("x")
	if len(x) == 0 {
		err = errors.New("x is empty")
		return
	}
	xInt, err = strconv.Atoi(x)
	if err != nil || xInt < 0 {
		err = fmt.Errorf("invalid x: %w", err)
		return
	}
	y := r.URL.Query().Get("y")
	if len(y) == 0 {
		err = errors.New("y is empty")
		return
	}
	yInt, err = strconv.Atoi(y)
	if err != nil || yInt < 0 {
		err = fmt.Errorf("invalid y: %w", err)
		return
	}
	z := r.URL.Query().Get("z")
	if len(z) == 0 {
		err = errors.New("z is empty")
		return
	}
	zInt, err = strconv.Atoi(z)
	if err != nil {
		err = fmt.Errorf("invalid z: %w", err)
		return
	}
	if zInt < 0 {
		err = errors.New("invalid z: must be non-negative")
		return
	}

	return
}

func parseProvider(r *http.Request) (provider int) {
	mapProvider := r.URL.Query().Get("provider")
	switch mapProvider {
	case MapProviderGoogle:
		provider = int(dbproto.MapProviderEnum_ProviderGoogle)
	case MapProviderTianDiTu:
		provider = int(dbproto.MapProviderEnum_ProviderTianditu)
	case MapProviderOSM:
		provider = int(dbproto.MapProviderEnum_ProviderOSM)
	default:
		provider = -1
	}

	return
}

func respondHttpError(w http.ResponseWriter, r *http.Request, message string, status int) {
	if r.Context().Err() != nil {
		if config.IsVerboseDebugMap {
			slog.Debug(
				"respond error to map req but context is canceled, ignore",
				"status",
				status,
				"err",
				message,
				"url",
				r.URL.String(),
			)
		}
		return
	}
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	w.WriteHeader(status)
	_, err := w.Write([]byte(message))
	if config.IsVerboseDebugMap {
		slog.Debug("respond error to map req", "status", status, "err", message, "url", r.URL.String())
	}
	if err != nil {
		slog.Warn("respond fail", "status", status, "err", err, "url", r.URL.String())
	}
}

func respondImageBytes(w http.ResponseWriter, r *http.Request, mapReq *MapReq, tileInfo *TileInfo, imageByte []byte) {
	if r.Context().Err() != nil {
		if config.IsVerboseDebugMap {
			slog.Debug(
				"respond image to map req but context is canceled, ignore",
				"len",
				len(imageByte),
				"url",
				r.URL.String(),
			)
		}
		return
	}
	// check if client has cached content
	if tileInfo != nil && r.Header.Get("Cache-Control") != "no-cache" {
		notMatch := r.Header.Get("If-None-Match")
		if len(notMatch) > 0 && len(tileInfo.Hash) > 0 && strings.Trim(notMatch, `"`) == tileInfo.Hash {
			w.WriteHeader(http.StatusNotModified)
			return
		}

		parse, err := time.Parse(http.TimeFormat, r.Header.Get("If-Modified-Since"))
		if err == nil && parse.UTC().Unix() >= tileInfo.CacheTime {
			w.WriteHeader(http.StatusNotModified)
			return
		}
	}

	var contentType string
	switch mapReq.MapType {
	case MapTypeRoadmap:
		contentType = "image/png"
	case MapTypeSatellite, MapTypeHybrid:
		contentType = "image/jpeg"
	default:
		contentType = "application/octet-stream"
	}

	header := w.Header()
	header.Set("Content-Type", contentType)
	header.Set("Content-Length", strconv.Itoa(len(imageByte)))

	// set http cache headers
	if tileInfo != nil {
		header.Set(MapCacheTimeHeaderName, strconv.FormatInt(tileInfo.CacheTime, 10))
		header.Set("Cache-Control", "no-cache") //always check for content updates while reusing stored content
		header.Set("ETag", fmt.Sprintf(`"%s"`, tileInfo.Hash))
		header.Set("Last-Modified", time.Unix(tileInfo.CacheTime, 0).Format(http.TimeFormat))
		//set tile status
		header.Set(MapTileStatusHeaderName, strconv.Itoa(tileInfo.Status))
	}

	switch mapReq.Provider {
	case int(dbproto.MapProviderEnum_ProviderGoogle):
		header.Set(MapProviderHeaderName, MapProviderGoogle)
	case int(dbproto.MapProviderEnum_ProviderTianditu):
		header.Set(MapProviderHeaderName, MapProviderTianDiTu)
	case int(dbproto.MapProviderEnum_ProviderOSM):
		header.Set(MapProviderHeaderName, MapProviderOSM)
	}

	w.WriteHeader(http.StatusOK)

	_, err := w.Write(imageByte)
	if config.IsVerboseDebugMap {
		slog.Debug("respond image bytes", "len", len(imageByte), "Content-Type", contentType, "url", r.URL.String())
	}
	if err != nil {
		slog.Error("respond image bytes fail", "err", err)
	}
}

func QueryTileIndexFromDb(mapReq *MapReq) (MapTileCacheIndex, error) {
	var index MapTileCacheIndex
	var err error
	switch mapReq.MapType {
	case MapTypeRoadmap:
		index, err = GetDbMapCacheRoadmapIndex(
			mapReq.Provider, mapReq.Lang, mapReq.X, mapReq.Y, mapReq.Z, mapReq.ImageFormatInt, mapReq.EnableGcj02)
	case MapTypeSatellite:
		index, err = GetDbMapCacheSatelliteIndex(
			mapReq.Provider, mapReq.X, mapReq.Y, mapReq.Z, mapReq.ImageFormatInt, mapReq.EnableGcj02)
	case MapTypeHybrid:
		index, err = GetDbMapCacheHybridIndex(
			mapReq.Provider, mapReq.Lang, mapReq.X, mapReq.Y, mapReq.Z, mapReq.ImageFormatInt, mapReq.EnableGcj02)
	default:
		err = errors.New("unknown map type")
	}

	return index, err
}

func QueryTileFromProvider(
	userRid string,
	mapReq *MapReq,
	needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error) {
	defer func() {
		if err != nil && mapReq.Provider == int(dbproto.MapProviderEnum_ProviderOSM) {
			imageBytes, err = ReqMapTileFromDefaultOSM(mapReq.X, mapReq.Y, mapReq.Z)
			if config.IsVerboseDebugMap {
				slog.Debug(
					"QueryTileFromProvider fallback to req map tile from default osm",
					"map req key",
					mapReq.Key(),
					"err",
					err,
				)
			}
			if err != nil {
				return
			}
			tileInfo, err = SaveTileToDb(mapReq, imageBytes, needCreateNewIndex)
			if err != nil {
				slog.Warn("save tile to db fail", "map req key", mapReq.Key(), "err", err)
				return
			}
		}
	}()
	var providerTokens []*MapProviderToken
	providerTokens, err = GetMapProviderTokensWithAdminFallback(userRid, mapReq.Provider)
	if err != nil {
		// if map provider is osm but no token, try to request from default osm without token
		return nil, nil, err
	}

	index, err := chooseMapProviderTokenFromSlice(providerTokens)
	if err != nil {
		return nil, nil, errors.New("no valid provider token")
	}

	for ; index < len(providerTokens); index++ {
		providerToken := providerTokens[index]
		if !providerToken.IsValid() {
			continue
		}
		if providerToken.MinZoom > 0 && mapReq.Z < providerToken.MinZoom {
			continue
		}

		if providerToken.MaxZoom > 0 && mapReq.Z > providerToken.MaxZoom {
			continue
		}
		tileInfo, imageBytes, err = ReqMapFromProviderWithToken(providerToken, mapReq, needCreateNewIndex)
		if config.IsVerboseDebugMap {
			slog.Debug("query tile from provider",
				"userRid", userRid,
				"err", err)
		}
		if err == nil {
			return tileInfo, imageBytes, nil
		}
		// Mark token as failed for this request
		GlobalMapsManager.SetMapProviderTokenLastFailTime(providerToken.TokenRid, bfutil.CurrentUTCTime())
	}

	return nil, nil, errors.New("failed to request with all provider tokens and admin fallback")
}

// QueryTileFromLocalDirectory attempts to get tile from local directory providers
// It acts as a fallback mechanism between cache and online services
func QueryTileFromLocalDirectory(userRid string, mapReq *MapReq) (*TileInfo, []byte, error) {
	// Get all provider tokens for this user and provider
	localProviderType, err := getLocalDirectoryTypeFromOnlineProvider(mapReq.Provider)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get local provider type: %w", err)
	}
	allTokens, err := GetMapProviderTokensWithAdminFallback(userRid, int(localProviderType))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get provider tokens: %w", err)
	}

	// Separate local directory tokens from online tokens
	localTokens, _ := SeparateTokensByType(allTokens)
	if len(localTokens) == 0 {
		return nil, nil, fmt.Errorf("no local directory tokens available for provider %d", int(localProviderType))
	}

	// Apply language matching and sorting for local directory tokens
	matchResults, err := GetLanguageMatchedTokens(localTokens, mapReq.Lang)
	if err != nil {
		return nil, nil, fmt.Errorf("language matching failed: %w", err)
	}

	// Sort by language confidence and priority
	sortedResults := SortTokensByConfidence(matchResults)

	if config.IsVerboseDebugMap {
		slog.Debug("QueryTileFromLocalDirectory language matching results",
			"userRid", userRid,
			"requestedLang", mapReq.Lang,
			"totalLocalTokens", len(localTokens),
			"sortedResults", len(sortedResults))
	}

	// Try each token in order of preference
	for _, result := range sortedResults {
		token := result.Token

		// Skip invalid tokens
		if !token.IsValid() {
			continue
		}

		// Check zoom level constraints
		if token.MinZoom > 0 && mapReq.Z < token.MinZoom {
			continue
		}
		if token.MaxZoom > 0 && mapReq.Z > token.MaxZoom {
			continue
		}

		// Attempt to read tile from local directory
		tileInfo, imageBytes, err := ReadTileFromLocalDirectory(token, mapReq)
		if err != nil {
			if config.IsVerboseDebugMap {
				slog.Debug("ReadTileFromLocalDirectory failed, trying next token",
					"tokenRid", token.TokenRid,
					"provider", token.Provider,
					"language", token.Language,
					"confidence", result.Confidence,
					"error", err.Error())
			}
			continue
		}

		if config.IsVerboseDebugMap {
			slog.Debug("ReadTileFromLocalDirectory succeeded",
				"tokenRid", token.TokenRid,
				"provider", token.Provider,
				"language", token.Language,
				"confidence", result.Confidence,
				"imageSize", len(imageBytes))
		}

		return tileInfo, imageBytes, nil
	}

	return nil, nil, fmt.Errorf("all local directory tokens failed for provider %d", mapReq.Provider)
}

func getLocalDirectoryTypeFromOnlineProvider(provider int) (dbproto.MapProviderEnum, error) {
	switch dbproto.MapProviderEnum(provider) {
	case dbproto.MapProviderEnum_ProviderGoogle, dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
		return dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, nil
	case dbproto.MapProviderEnum_ProviderTianditu, dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
		return dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, nil
	case dbproto.MapProviderEnum_ProviderOSM, dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
		return dbproto.MapProviderEnum_ProviderOSMLocalDirectory, nil

	default:
		return -1, fmt.Errorf("unknown provider: %d", provider)
	}
}

// ReadTileFromLocalDirectory reads a tile from local directory based on token configuration
func ReadTileFromLocalDirectory(token *MapProviderToken, mapReq *MapReq) (*TileInfo, []byte, error) {
	if token == nil {
		slog.Error("ReadTileFromLocalDirectory called with nil token")
		return nil, nil, fmt.Errorf("token cannot be nil")
	}

	if !IsLocalDirectoryProvider(token.Provider) {
		slog.Warn("ReadTileFromLocalDirectory called with non-local-directory provider",
			"provider", token.Provider,
			"tokenRid", token.TokenRid)
		return nil, nil, fmt.Errorf("token provider %v is not a local directory provider", token.Provider)
	}

	// Build the file path based on provider type and configuration
	filePath, err := buildLocalDirectoryPath(token, mapReq)
	if err != nil {
		slog.Error("Failed to build local directory path",
			"error", err,
			"tokenRid", token.TokenRid,
			"provider", token.Provider,
			"baseUrl", token.BaseUrl,
			"x", mapReq.X, "y", mapReq.Y, "z", mapReq.Z)
		return nil, nil, fmt.Errorf("failed to build local directory path: %w", err)
	}

	// Security check: ensure the path is within the base directory
	if err := validatePathSecurity(token.BaseUrl, filePath); err != nil {
		slog.Warn("Path security validation failed",
			"error", err,
			"tokenRid", token.TokenRid,
			"baseUrl", token.BaseUrl,
			"filePath", filePath)
		return nil, nil, fmt.Errorf("path security validation failed: %w", err)
	}

	// Check if file exists before attempting to read (for better error reporting)
	if _, err := os.Stat(filePath); err != nil {
		if os.IsNotExist(err) {
			slog.Debug("Tile file not found in local directory",
				"filePath", filePath,
				"tokenRid", token.TokenRid,
				"x", mapReq.X, "y", mapReq.Y, "z", mapReq.Z)
			return nil, nil, fmt.Errorf("tile file not found: %s", filePath)
		}
		if os.IsPermission(err) {
			slog.Error("Permission denied accessing tile file",
				"filePath", filePath,
				"tokenRid", token.TokenRid,
				"error", err)
			return nil, nil, fmt.Errorf("permission denied accessing tile file: %s", filePath)
		}
		slog.Error("Failed to stat tile file",
			"filePath", filePath,
			"tokenRid", token.TokenRid,
			"error", err)
		return nil, nil, fmt.Errorf("failed to access tile file %s: %w", filePath, err)
	}

	// Read the tile file
	imageBytes, err := os.ReadFile(filePath)
	if err != nil {
		slog.Error("Failed to read tile file",
			"filePath", filePath,
			"tokenRid", token.TokenRid,
			"error", err,
			"x", mapReq.X, "y", mapReq.Y, "z", mapReq.Z)

		if os.IsNotExist(err) {
			return nil, nil, fmt.Errorf("tile file not found: %s", filePath)
		}
		if os.IsPermission(err) {
			return nil, nil, fmt.Errorf("permission denied reading tile file: %s", filePath)
		}
		return nil, nil, fmt.Errorf("failed to read tile file %s: %w", filePath, err)
	}

	if config.IsVerboseDebugMap {
		slog.Debug("Successfully read tile from local directory",
			"filePath", filePath,
			"tokenRid", token.TokenRid,
			"fileSize", len(imageBytes),
			"x", mapReq.X, "y", mapReq.Y, "z", mapReq.Z)
	}

	// Create TileInfo
	tileInfo := &TileInfo{
		CacheTime: bfutil.CurrentUTCTime().Unix(),
		Status:    1, // Success status
	}

	return tileInfo, imageBytes, nil
}

// buildLocalDirectoryPath constructs the file path for local directory providers
func buildLocalDirectoryPath(token *MapProviderToken, mapReq *MapReq) (string, error) {
	if token.BaseUrl == "" {
		return "", fmt.Errorf("token BaseUrl cannot be empty")
	}

	// Determine file extension based on image format
	ext := "png"
	if mapReq.ImageFormat == TileImageFormatJpg {
		ext = "jpg"
	}

	// Build path based on provider type
	baseUrl, err := filepath.EvalSymlinks(token.BaseUrl)
	if err != nil {
		return "", fmt.Errorf("failed to resolve base URL: %w", err)
	}
	baseUrl = strings.TrimRight(baseUrl, "/")
	// Format: {BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}
	return fmt.Sprintf("%s/%s/%d/%d/%d.%s",
		baseUrl, mapReq.MapType, mapReq.Z, mapReq.X, mapReq.Y, ext), nil
}

// validatePathSecurity ensures the file path is within the base directory to prevent path traversal
func validatePathSecurity(baseUrl, filePath string) error {
	if baseUrl == "" {
		return fmt.Errorf("base URL cannot be empty")
	}
	if filePath == "" {
		return fmt.Errorf("file path cannot be empty")
	}

	// Clean the paths to resolve any .. or . components
	cleanBase, err := filepath.Abs(filepath.Clean(baseUrl))
	if err != nil {
		return fmt.Errorf("failed to resolve absolute base path: %w", err)
	}

	cleanFile, err := filepath.Abs(filepath.Clean(filePath))
	if err != nil {
		return fmt.Errorf("failed to resolve absolute file path: %w", err)
	}

	// Check if the file path starts with the base path
	if !strings.HasPrefix(cleanFile, cleanBase) {
		return fmt.Errorf("file path %s is outside base directory %s", cleanFile, cleanBase)
	}

	// Additional check for path traversal attempts in the original path
	if strings.Contains(filePath, "..") {
		return fmt.Errorf("path traversal detected in file path: %s", filePath)
	}

	// Check for other suspicious patterns
	suspiciousPatterns := []string{"~", "//", "\\", "%2e%2e", "%2f"}
	for _, pattern := range suspiciousPatterns {
		if strings.Contains(strings.ToLower(filePath), pattern) {
			return fmt.Errorf("suspicious pattern '%s' detected in file path: %s", pattern, filePath)
		}
	}

	return nil
}

func ReqMapFromProviderWithToken(
	providerToken *MapProviderToken,
	mapReq *MapReq,
	needCreateNewIndex bool,
) (*TileInfo, []byte, error) {
	if providerToken.IsQuotasOver() {
		return nil, nil, errors.New("quotas over")
	}
	if providerToken.IsExpired() {
		return nil, nil, errors.New("token is expired")
	}
	if !providerToken.IsActive() {
		return nil, nil, errors.New("token is disabled")
	}

	var tileInfo *TileInfo
	var imageBytes []byte
	var err error
	switch providerToken.Provider {
	case dbproto.MapProviderEnum_ProviderGoogle:
		if providerToken.GoogleMapApi == dbproto.GoogleMapApiEnum_GoogleMapApiStatic {
			imageBytes, err = ReqGoogleMapStaticWithGcj02Convert(providerToken, mapReq)
		} else {
			imageBytes, err = ReqGoogleMapTileWithGcj02Convert(providerToken, mapReq)
		}
	case dbproto.MapProviderEnum_ProviderTianditu:
		imageBytes, err = providerToken.ReqTiandutuMapTile(mapReq)
	case dbproto.MapProviderEnum_ProviderOSM:
		imageBytes, err = providerToken.ReqOSMMapTile(mapReq)
	default:
		return nil, nil, errors.New("not support provider")
	}
	if err != nil {
		return nil, nil, fmt.Errorf("req map from provider fail: %w", err)
	}

	if len(imageBytes) == 0 {
		return nil, nil, errors.New("image bytes is empty")
	}

	tileInfo, err = SaveTileToDb(mapReq, imageBytes, needCreateNewIndex)
	if err != nil {
		slog.Warn("ReqMapFromProviderWithToken SaveTileToDb fail", "req", mapReq.Key(), "err", err)
	}

	return tileInfo, imageBytes, nil
}

// save tile to db,needCreate means index not exist,create it.
func SaveTileToDb(mapReq *MapReq, imageBytes []byte, needCreateNewIndex bool) (tileInfo *TileInfo, err error) {
	// calculate hash
	u := xxh3.Hash128(imageBytes).Bytes()
	saveHash := base64.StdEncoding.EncodeToString(u[:])
	cacheTime := bfutil.CurrentUTCTime().Unix()

	// save index to db
	var oldIndex MapTileCacheIndex
	var isSaved bool
	switch mapReq.MapType {
	case MapTypeRoadmap:
		oldIndex, isSaved, err = saveDbMapCacheRoadmapIndex(mapReq, saveHash, cacheTime, needCreateNewIndex)
	case MapTypeSatellite:
		oldIndex, isSaved, err = saveDbMapCacheSatelliteIndex(mapReq, saveHash, cacheTime, needCreateNewIndex)
	case MapTypeHybrid:
		oldIndex, isSaved, err = saveDbMapCacheHybridIndex(mapReq, saveHash, cacheTime, needCreateNewIndex)
	default:
		err = errors.New("unknown map type")
	}
	if config.IsVerboseDebugMap {
		slog.Debug("save tile index to db", "hash", saveHash, "is saved", isSaved, "map req key", mapReq.Key())
	}
	if err != nil {
		return nil, err
	}

	if !isSaved {
		// no save in db, no need save to key-value db
		return &TileInfo{
			Hash:      oldIndex.GetTileHash(),
			CacheTime: oldIndex.GetCacheTime(),
			Status:    int(oldIndex.GetStatus()),
		}, nil
	}

	tileInfo = &TileInfo{Hash: saveHash, CacheTime: cacheTime, Status: 1}
	err = SaveTileToKeyValueDb(mapReq.Key(), tileInfo, imageBytes)
	if config.IsVerboseDebugMap {
		slog.Debug(
			"save tile to key-value db",
			"hash",
			saveHash,
			"size",
			len(imageBytes),
			"map req key",
			mapReq.Key(),
			"err",
			err,
		)
	}

	if oldIndex == nil {
		return tileInfo, err
	}

	oldIndexHash := oldIndex.GetTileHash()
	if oldIndexHash != "" && oldIndexHash != saveHash {
		err = db.KeyValueDbDelete([]byte(oldIndexHash))
		if config.IsVerboseDebugMap {
			slog.Debug(
				"delete old tile to key-value db",
				"oldHash",
				oldIndexHash,
				"map req key",
				mapReq.Key(),
				"err",
				err,
			)
		}
	}

	return tileInfo, err
}

func saveDbMapCacheHybridIndex(
	mapReq *MapReq,
	newHash string,
	cacheTime int64,
	needCreateNewIndex bool,
) (oldIndex *dbproto.DbMapCacheHybridIndex, isSaved bool, err error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, false, err
	}

	index := &dbproto.DbMapCacheHybridIndex{
		Rid:             bfutil.UuidV7(),
		Provider:        dbproto.MapProviderEnum(mapReq.Provider),
		Language:        mapReq.Lang,
		TileX:           int32(mapReq.X),
		TileY:           int32(mapReq.Y),
		TileZ:           int32(mapReq.Z),
		CacheTime:       cacheTime,
		AccessTime:      cacheTime,
		TileImageFormat: int32(mapReq.ImageFormatInt),
		TileHash:        newHash,
		Gcj02:           int32(mapReq.EnableGcj02),
		Status:          1,
	}

	if needCreateNewIndex {
		_, err = crud.DbInsert(dbConn.WriteDB, index, 0, "")
		if err == nil {
			return nil, true, nil
		}
	}

	keyColumns := []string{"Provider", "Language", "TileX", "TileY", "TileZ", "TileImageFormat", "Gcj02"}
	oldMsg, err := crud.DbSelectOne(dbConn.ReadDB, index, keyColumns, nil, "", false)
	if err != nil {
		return nil, false, fmt.Errorf("select old DbMapCacheHybridIndex fail: %w", err)
	}
	oldIndex = oldMsg.(*dbproto.DbMapCacheHybridIndex)

	if oldIndex.Status != 8 &&
		(oldIndex.TileHash == newHash || bfutil.CurrentUTCTimeUnix()-oldIndex.CacheTime < WeekInSeconds) {
		// same hash or old hash cache in week, do nothing and return
		return oldIndex, false, nil
	}

	index.Rid = oldIndex.Rid
	_, err = crud.DbUpdatePartial(dbConn.WriteDB, index, []string{"CacheTime", "AccessTime", "TileHash", "Status"}, "")
	return oldIndex, true, err
}

func saveDbMapCacheSatelliteIndex(
	mapReq *MapReq,
	newHash string,
	cacheTime int64,
	needCreateNewIndex bool,
) (oldIndex *dbproto.DbMapCacheSatelliteIndex, isSaved bool, err error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, false, err
	}

	index := &dbproto.DbMapCacheSatelliteIndex{
		Rid:             bfutil.UuidV7(),
		Provider:        dbproto.MapProviderEnum(mapReq.Provider),
		TileX:           int32(mapReq.X),
		TileY:           int32(mapReq.Y),
		TileZ:           int32(mapReq.Z),
		CacheTime:       cacheTime,
		AccessTime:      cacheTime,
		TileImageFormat: int32(mapReq.ImageFormatInt),
		TileHash:        newHash,
		Gcj02:           int32(mapReq.EnableGcj02),
		Status:          1,
	}

	if needCreateNewIndex {
		_, err = crud.DbInsert(dbConn.WriteDB, index, 0, "")
		if err == nil {
			return nil, true, nil
		}
	}

	keyColumns := []string{"Provider", "TileX", "TileY", "TileZ", "TileImageFormat", "Gcj02"}
	oldMsg, err := crud.DbSelectOne(dbConn.ReadDB, index, keyColumns, nil, "", false)
	if err != nil {
		return nil, false, fmt.Errorf("select old DbMapCacheSatelliteIndex fail: %w", err)
	}
	oldIndex = oldMsg.(*dbproto.DbMapCacheSatelliteIndex)

	if oldIndex.Status != 8 &&
		(oldIndex.TileHash == newHash || bfutil.CurrentUTCTimeUnix()-oldIndex.CacheTime < WeekInSeconds) {
		// same hash or old hash cache in week, do nothing and return
		return oldIndex, false, nil
	}

	index.Rid = oldIndex.Rid
	_, err = crud.DbUpdatePartial(dbConn.WriteDB, index, []string{"CacheTime", "AccessTime", "TileHash", "Status"}, "")
	return oldIndex, true, err
}

// if needCreateNewIndex is true, create new index,
// else update old index, and return old index hash.
// may not save index to db if hash is same and tile is cached in week, isSaved is false
func saveDbMapCacheRoadmapIndex(
	mapReq *MapReq,
	newHash string,
	cacheTime int64,
	needCreateNewIndex bool,
) (oldIndex *dbproto.DbMapCacheRoadmapIndex, isSaved bool, err error) {
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, false, err
	}

	index := &dbproto.DbMapCacheRoadmapIndex{
		Rid:             bfutil.UuidV7(),
		Provider:        dbproto.MapProviderEnum(mapReq.Provider),
		Language:        mapReq.Lang,
		TileX:           int32(mapReq.X),
		TileY:           int32(mapReq.Y),
		TileZ:           int32(mapReq.Z),
		CacheTime:       cacheTime,
		AccessTime:      cacheTime,
		TileImageFormat: int32(mapReq.ImageFormatInt),
		TileHash:        newHash,
		Gcj02:           int32(mapReq.EnableGcj02),
		Status:          1,
	}

	if needCreateNewIndex {
		_, err = crud.DbInsert(dbConn.WriteDB, index, 0, "")
		if err == nil {
			return nil, true, nil
		}
	}

	keyColumns := []string{"Provider", "Language", "TileX", "TileY", "TileZ", "TileImageFormat", "Gcj02"}
	oldMsg, err := crud.DbSelectOne(dbConn.ReadDB, index, keyColumns, nil, "", false)
	if err != nil {
		return nil, false, fmt.Errorf("select old DbMapCacheRoadmapIndex fail: %w", err)
	}
	oldIndex = oldMsg.(*dbproto.DbMapCacheRoadmapIndex)

	if oldIndex.Status != 8 &&
		(oldIndex.TileHash == newHash || bfutil.CurrentUTCTimeUnix()-oldIndex.CacheTime < WeekInSeconds) {
		// same hash or old hash cache in week, do nothing and return
		return oldIndex, false, nil
	}

	index.Rid = oldIndex.Rid
	_, err = crud.DbUpdatePartial(dbConn.WriteDB, index, []string{"CacheTime", "AccessTime", "TileHash", "Status"}, "")
	return oldIndex, true, err
}

func natsHandlerForMapReq(msg *nats.Msg) {
	if msg.Header.Get(bfnats.NatsClientIDHeader) == bfnats.GetClientID() {
		return
	}

	mapReq := &MapReq{}
	err := json.Unmarshal(msg.Data, mapReq)
	if err != nil {
		slog.Warn("natsHandlerForMapReq unmarshal fail", "err", err, "msgData", string(msg.Data))
		return
	}

	if config.IsVerboseDebugMap {
		slog.Debug("natsHandlerForMapReq", "mapReq", mapReq)
	}

	_, imageBytes, err := QueryTileFromKeyValueDb(mapReq)
	if err != nil {
		// 缓存查询失败，尝试本地目录查询
		// NATS环境使用AdminRid来获取系统级别的本地目录tokens
		_, imageBytes, err = QueryTileFromLocalDirectory(config.AdminRid, mapReq)
		if err != nil {
			// 本地目录也失败，不响应NATS请求，让请求方继续在线服务
			if config.IsVerboseDebugMap {
				slog.Debug(
					"natsHandlerForMapReq query tile from db and local directory both fail",
					"err",
					err,
					"msgData",
					string(msg.Data),
				)
			}
			return
		}

		// 本地目录查询成功，保存到缓存
		_, saveErr := SaveTileToDb(mapReq, imageBytes, true)
		if saveErr != nil && config.IsVerboseDebugMap {
			slog.Debug("natsHandlerForMapReq save tile to db fail", "err", saveErr)
		}
	}

	err = msg.Respond(imageBytes)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("natsHandlerForMapReq respond fail", "err", err, "msgData", string(msg.Data))
		}
		return
	}
}

func MapSubscribeNats() (*nats.Subscription, error) {
	return bfnats.NatsSubscribe(mapReqNatSubject, natsHandlerForMapReq)
}
