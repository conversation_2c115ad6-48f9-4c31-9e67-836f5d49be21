package maps

import (
	"bfmap/dbproto"
	"fmt"
	"testing"

	"golang.org/x/text/language"
)

// Benchmark tests for local directory fallback feature performance

// BenchmarkLanguageMatching tests the performance of language matching algorithms
func BenchmarkLanguageMatching(b *testing.B) {
	// Create a large set of tokens for benchmarking
	tokens := make([]*MapProviderToken, 100)
	for i := 0; i < 100; i++ {
		tokens[i] = &MapProviderToken{
			TokenRid: generateTokenRid(i),
			Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			Language: generateLanguage(i),
			Priority: int32(i%10 + 1), // Priority 1-10
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := GetLanguageMatchedTokens(tokens, "zh-CN")
		if err != nil {
			b.Fatalf("GetLanguageMatchedTokens failed: %v", err)
		}
	}
}

// BenchmarkSortTokensByConfidence tests the performance of token sorting
func BenchmarkSortTokensByConfidence(b *testing.B) {
	// Create language match results for benchmarking
	results := make([]LanguageMatchResult, 100)
	confidences := []language.Confidence{
		language.Exact,
		language.High,
		language.Low,
		language.No,
	} // Different confidence levels

	for i := 0; i < 100; i++ {
		results[i] = LanguageMatchResult{
			Token: &MapProviderToken{
				TokenRid: generateTokenRid(i),
				Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
				Language: generateLanguage(i),
				Priority: int32(i%10 + 1),
			},
			Confidence: confidences[i%4], // Cycling through confidence levels
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = SortTokensByConfidence(results)
	}
}

// BenchmarkValidateAndNormalizeLanguage tests language validation performance
func BenchmarkValidateAndNormalizeLanguage(b *testing.B) {
	languages := []string{
		"zh-CN", "en-US", "ja-JP", "ko-KR", "fr-FR",
		"de-DE", "es-ES", "it-IT", "pt-BR", "ru-RU",
		"zh", "en", "ja", "ko", "fr",
		"invalid-lang", "toolong", "",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		lang := languages[i%len(languages)]
		_, _ = ValidateAndNormalizeLanguage(lang)
	}
}

// BenchmarkSeparateTokensByType tests token separation performance
func BenchmarkSeparateTokensByType(b *testing.B) {
	// Create mixed tokens (local and online)
	tokens := make([]*MapProviderToken, 100)
	localProviders := []dbproto.MapProviderEnum{
		dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
		dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
		dbproto.MapProviderEnum_ProviderOSMLocalDirectory,
	}
	onlineProviders := []dbproto.MapProviderEnum{
		dbproto.MapProviderEnum_ProviderGoogle,
		dbproto.MapProviderEnum_ProviderTianditu,
		dbproto.MapProviderEnum_ProviderOSM,
	}

	for i := 0; i < 100; i++ {
		if i%2 == 0 {
			tokens[i] = &MapProviderToken{
				TokenRid: generateTokenRid(i),
				Provider: localProviders[i%3],
				Language: generateLanguage(i),
			}
		} else {
			tokens[i] = &MapProviderToken{
				TokenRid: generateTokenRid(i),
				Provider: onlineProviders[i%3],
				Language: generateLanguage(i),
			}
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = SeparateTokensByType(tokens)
	}
}

// BenchmarkBuildLocalDirectoryPath tests path building performance
func BenchmarkBuildLocalDirectoryPath(b *testing.B) {
	token := &MapProviderToken{
		Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
		BaseUrl:  "/test/data/google",
		Language: "zh-CN",
	}

	mapReq := &MapReq{
		MapType:     "satellite",
		Z:           16,
		X:           53393,
		Y:           54321,
		ImageFormat: TileImageFormatJpg,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := buildLocalDirectoryPath(token, mapReq)
		if err != nil {
			b.Fatalf("buildLocalDirectoryPath failed: %v", err)
		}
	}
}

// BenchmarkCompleteLanguageMatchingWorkflow tests the complete workflow
func BenchmarkCompleteLanguageMatchingWorkflow(b *testing.B) {
	// Create a realistic set of tokens
	tokens := make([]*MapProviderToken, 50)
	for i := 0; i < 50; i++ {
		tokens[i] = &MapProviderToken{
			TokenRid: generateTokenRid(i),
			Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			Language: generateLanguage(i),
			Priority: int32(i%5 + 1), // Priority 1-5
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Complete workflow: match languages -> sort by confidence
		matchResults, err := GetLanguageMatchedTokens(tokens, "zh-CN")
		if err != nil {
			b.Fatalf("GetLanguageMatchedTokens failed: %v", err)
		}
		_ = SortTokensByConfidence(matchResults)
	}
}

// Benchmark with varying token counts to test scalability
func BenchmarkLanguageMatchingScalability(b *testing.B) {
	tokenCounts := []int{10, 50, 100, 500, 1000}

	for _, count := range tokenCounts {
		b.Run(fmt.Sprintf("Tokens_%d", count), func(b *testing.B) {
			tokens := make([]*MapProviderToken, count)
			for i := 0; i < count; i++ {
				tokens[i] = &MapProviderToken{
					TokenRid: generateTokenRid(i),
					Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
					Language: generateLanguage(i),
					Priority: int32(i%10 + 1),
				}
			}

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_, err := GetLanguageMatchedTokens(tokens, "zh-CN")
				if err != nil {
					b.Fatalf("GetLanguageMatchedTokens failed: %v", err)
				}
			}
		})
	}
}

// BenchmarkConcurrentLanguageMatching tests concurrent access performance
func BenchmarkConcurrentLanguageMatching(b *testing.B) {
	tokens := make([]*MapProviderToken, 100)
	for i := 0; i < 100; i++ {
		tokens[i] = &MapProviderToken{
			TokenRid: generateTokenRid(i),
			Provider: dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			Language: generateLanguage(i),
			Priority: int32(i%10 + 1),
		}
	}

	languages := []string{"zh-CN", "en-US", "ja-JP", "ko-KR", "fr-FR"}

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		langIndex := 0
		for pb.Next() {
			lang := languages[langIndex%len(languages)]
			langIndex++
			_, err := GetLanguageMatchedTokens(tokens, lang)
			if err != nil {
				b.Fatalf("GetLanguageMatchedTokens failed: %v", err)
			}
		}
	})
}

// Helper functions for generating test data

func generateTokenRid(i int) string {
	return fmt.Sprintf("test-token-%d", i)
}

func generateLanguage(i int) string {
	languages := []string{
		"zh-CN", "en-US", "ja-JP", "ko-KR", "fr-FR",
		"de-DE", "es-ES", "it-IT", "pt-BR", "ru-RU",
		"ar-SA", "hi-IN", "th-TH", "vi-VN", "tr-TR",
		"pl-PL", "nl-NL", "sv-SE", "da-DK", "no-NO",
	}
	return languages[i%len(languages)]
}
