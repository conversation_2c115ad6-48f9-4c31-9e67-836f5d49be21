package maps

import (
	"math"
	"testing"
)

func TestIsOutOfChina(t *testing.T) {
	tests := []struct {
		name string
		lat  float64
		lng  float64
		want bool
	}{
		{
			name: "Beijing - inside China",
			lat:  39.9042,
			lng:  116.4074,
			want: false,
		},
		{
			name: "Shanghai - inside China",
			lat:  31.2304,
			lng:  121.4737,
			want: false,
		},
		{
			name: "Guangzhou - inside China",
			lat:  23.1291,
			lng:  113.2644,
			want: false,
		},
		{
			name: "Urumqi - inside China",
			lat:  43.8256,
			lng:  87.6168,
			want: false,
		},
		{
			name: "Harbin - inside China",
			lat:  45.8038,
			lng:  126.5349,
			want: false,
		},
		{
			name: "Tokyo - outside China",
			lat:  35.6762,
			lng:  139.6503,
			want: true,
		},
		{
			name: "New York - outside China",
			lat:  40.7128,
			lng:  -74.0060,
			want: true,
		},
		{
			name: "London - outside China",
			lat:  51.5074,
			lng:  -0.1278,
			want: true,
		},
		{
			name: "Moscow - outside China",
			lat:  55.7558,
			lng:  37.6176,
			want: true,
		},
		{
			name: "Western boundary - outside",
			lat:  40.0,
			lng:  71.0,
			want: true,
		},
		{
			name: "Eastern boundary - outside",
			lat:  40.0,
			lng:  138.0,
			want: true,
		},
		{
			name: "Southern boundary - outside",
			lat:  0.5,
			lng:  110.0,
			want: true,
		},
		{
			name: "Northern boundary - outside",
			lat:  56.0,
			lng:  110.0,
			want: true,
		},
		{
			name: "Edge case - western boundary",
			lat:  40.0,
			lng:  72.004,
			want: false,
		},
		{
			name: "Edge case - eastern boundary",
			lat:  40.0,
			lng:  137.8347,
			want: false,
		},
		{
			name: "Edge case - southern boundary",
			lat:  0.8293,
			lng:  110.0,
			want: false,
		},
		{
			name: "Edge case - northern boundary",
			lat:  55.8271,
			lng:  110.0,
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isOutOFChina(tt.lat, tt.lng); got != tt.want {
				t.Errorf("isOutOFChina() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTransform(t *testing.T) {
	tests := []struct {
		name     string
		x, y     float64
		wantLat  float64
		wantLng  float64
		tolerance float64
	}{
		{
			name:      "Origin point",
			x:         0.0,
			y:         0.0,
			wantLat:   -100.0,
			wantLng:   300.0,
			tolerance: 0.1,
		},
		{
			name:      "Positive coordinates",
			x:         10.0,
			y:         20.0,
			tolerance: 1.0, // Allow larger tolerance for complex calculations
		},
		{
			name:      "Negative coordinates",
			x:         -10.0,
			y:         -20.0,
			tolerance: 1.0,
		},
		{
			name:      "Large coordinates",
			x:         100.0,
			y:         50.0,
			tolerance: 10.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotLat, gotLng := transform(tt.x, tt.y)
			
			// For origin point, we can check exact values
			if tt.name == "Origin point" {
				if math.Abs(gotLat-tt.wantLat) > tt.tolerance {
					t.Errorf("transform() lat = %v, want %v", gotLat, tt.wantLat)
				}
				if math.Abs(gotLng-tt.wantLng) > tt.tolerance {
					t.Errorf("transform() lng = %v, want %v", gotLng, tt.wantLng)
				}
			} else {
				// For other cases, just ensure the function doesn't panic and returns reasonable values
				if math.IsNaN(gotLat) || math.IsInf(gotLat, 0) {
					t.Errorf("transform() returned invalid lat: %v", gotLat)
				}
				if math.IsNaN(gotLng) || math.IsInf(gotLng, 0) {
					t.Errorf("transform() returned invalid lng: %v", gotLng)
				}
			}
		})
	}
}

func TestDelta(t *testing.T) {
	tests := []struct {
		name string
		lat  float64
		lng  float64
	}{
		{
			name: "Beijing coordinates",
			lat:  39.9042,
			lng:  116.4074,
		},
		{
			name: "Shanghai coordinates",
			lat:  31.2304,
			lng:  121.4737,
		},
		{
			name: "Guangzhou coordinates",
			lat:  23.1291,
			lng:  113.2644,
		},
		{
			name: "Edge case - zero coordinates",
			lat:  0.0,
			lng:  0.0,
		},
		{
			name: "Edge case - negative coordinates",
			lat:  -10.0,
			lng:  -10.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dLat, dLng := delta(tt.lat, tt.lng)
			
			// Ensure the function doesn't return invalid values
			if math.IsNaN(dLat) || math.IsInf(dLat, 0) {
				t.Errorf("delta() returned invalid dLat: %v", dLat)
			}
			if math.IsNaN(dLng) || math.IsInf(dLng, 0) {
				t.Errorf("delta() returned invalid dLng: %v", dLng)
			}
			
			// Delta values should be reasonable (not too large)
			if math.Abs(dLat) > 1.0 {
				t.Errorf("delta() dLat seems too large: %v", dLat)
			}
			if math.Abs(dLng) > 1.0 {
				t.Errorf("delta() dLng seems too large: %v", dLng)
			}
		})
	}
}

func TestWGStoGCJ(t *testing.T) {
	tests := []struct {
		name     string
		wgsLat   float64
		wgsLng   float64
		tolerance float64
	}{
		{
			name:      "Beijing",
			wgsLat:    39.9042,
			wgsLng:    116.4074,
			tolerance: 0.01,
		},
		{
			name:      "Shanghai",
			wgsLat:    31.2304,
			wgsLng:    121.4737,
			tolerance: 0.01,
		},
		{
			name:      "Guangzhou",
			wgsLat:    23.1291,
			wgsLng:    113.2644,
			tolerance: 0.01,
		},
		{
			name:      "Outside China - should return original",
			wgsLat:    35.6762, // Tokyo
			wgsLng:    139.6503,
			tolerance: 0.0001,
		},
		{
			name:      "Outside China - New York",
			wgsLat:    40.7128,
			wgsLng:    -74.0060,
			tolerance: 0.0001,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gcjLat, gcjLng := WGStoGCJ(tt.wgsLat, tt.wgsLng)
			
			// Ensure valid coordinates
			if math.IsNaN(gcjLat) || math.IsInf(gcjLat, 0) {
				t.Errorf("WGStoGCJ() returned invalid gcjLat: %v", gcjLat)
			}
			if math.IsNaN(gcjLng) || math.IsInf(gcjLng, 0) {
				t.Errorf("WGStoGCJ() returned invalid gcjLng: %v", gcjLng)
			}
			
			// For coordinates outside China, should return original values
			if isOutOFChina(tt.wgsLat, tt.wgsLng) {
				if math.Abs(gcjLat-tt.wgsLat) > tt.tolerance {
					t.Errorf("WGStoGCJ() for outside China lat = %v, want %v", gcjLat, tt.wgsLat)
				}
				if math.Abs(gcjLng-tt.wgsLng) > tt.tolerance {
					t.Errorf("WGStoGCJ() for outside China lng = %v, want %v", gcjLng, tt.wgsLng)
				}
			} else {
				// For coordinates inside China, should be different from original
				if math.Abs(gcjLat-tt.wgsLat) < 0.0001 && math.Abs(gcjLng-tt.wgsLng) < 0.0001 {
					t.Errorf("WGStoGCJ() for inside China should transform coordinates, got same values")
				}
			}
		})
	}
}

func TestGCJtoWGS(t *testing.T) {
	tests := []struct {
		name     string
		gcjLat   float64
		gcjLng   float64
		tolerance float64
	}{
		{
			name:      "Beijing area",
			gcjLat:    39.9042,
			gcjLng:    116.4074,
			tolerance: 0.01,
		},
		{
			name:      "Shanghai area",
			gcjLat:    31.2304,
			gcjLng:    121.4737,
			tolerance: 0.01,
		},
		{
			name:      "Outside China - should return original",
			gcjLat:    35.6762, // Tokyo
			gcjLng:    139.6503,
			tolerance: 0.0001,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wgsLat, wgsLng := GCJtoWGS(tt.gcjLat, tt.gcjLng)
			
			// Ensure valid coordinates
			if math.IsNaN(wgsLat) || math.IsInf(wgsLat, 0) {
				t.Errorf("GCJtoWGS() returned invalid wgsLat: %v", wgsLat)
			}
			if math.IsNaN(wgsLng) || math.IsInf(wgsLng, 0) {
				t.Errorf("GCJtoWGS() returned invalid wgsLng: %v", wgsLng)
			}
			
			// For coordinates outside China, should return original values
			if isOutOFChina(tt.gcjLat, tt.gcjLng) {
				if math.Abs(wgsLat-tt.gcjLat) > tt.tolerance {
					t.Errorf("GCJtoWGS() for outside China lat = %v, want %v", wgsLat, tt.gcjLat)
				}
				if math.Abs(wgsLng-tt.gcjLng) > tt.tolerance {
					t.Errorf("GCJtoWGS() for outside China lng = %v, want %v", wgsLng, tt.gcjLng)
				}
			}
		})
	}
}

func TestGCJtoWGSExact(t *testing.T) {
	tests := []struct {
		name     string
		gcjLat   float64
		gcjLng   float64
		tolerance float64
	}{
		{
			name:      "Beijing area",
			gcjLat:    39.9042,
			gcjLng:    116.4074,
			tolerance: 0.001, // More precise than regular GCJtoWGS
		},
		{
			name:      "Shanghai area",
			gcjLat:    31.2304,
			gcjLng:    121.4737,
			tolerance: 0.001,
		},
		{
			name:      "Outside China - should return original",
			gcjLat:    35.6762, // Tokyo
			gcjLng:    139.6503,
			tolerance: 0.0001,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wgsLat, wgsLng := GCJtoWGSExact(tt.gcjLat, tt.gcjLng)
			
			// Ensure valid coordinates
			if math.IsNaN(wgsLat) || math.IsInf(wgsLat, 0) {
				t.Errorf("GCJtoWGSExact() returned invalid wgsLat: %v", wgsLat)
			}
			if math.IsNaN(wgsLng) || math.IsInf(wgsLng, 0) {
				t.Errorf("GCJtoWGSExact() returned invalid wgsLng: %v", wgsLng)
			}
		})
	}
}

func TestDistance(t *testing.T) {
	tests := []struct {
		name     string
		latA     float64
		lngA     float64
		latB     float64
		lngB     float64
		expected float64
		tolerance float64
	}{
		{
			name:      "Same point",
			latA:      39.9042,
			lngA:      116.4074,
			latB:      39.9042,
			lngB:      116.4074,
			expected:  0.0,
			tolerance: 0.1,
		},
		{
			name:      "Beijing to Shanghai",
			latA:      39.9042,
			lngA:      116.4074,
			latB:      31.2304,
			lngB:      121.4737,
			expected:  1067000, // Approximately 1067 km
			tolerance: 50000,   // 50km tolerance
		},
		{
			name:      "Short distance",
			latA:      39.9042,
			lngA:      116.4074,
			latB:      39.9142, // 0.01 degree difference
			lngB:      116.4174,
			tolerance: 2000, // 2km tolerance
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			distance := Distance(tt.latA, tt.lngA, tt.latB, tt.lngB)
			
			// Ensure valid distance
			if math.IsNaN(distance) || math.IsInf(distance, 0) || distance < 0 {
				t.Errorf("Distance() returned invalid distance: %v", distance)
			}
			
			// Check expected distance if provided
			if tt.expected > 0 {
				if math.Abs(distance-tt.expected) > tt.tolerance {
					t.Errorf("Distance() = %v, want %v ± %v", distance, tt.expected, tt.tolerance)
				}
			}
		})
	}
}

// Test coordinate conversion round-trip accuracy
func TestCoordinateConversionRoundTrip(t *testing.T) {
	tests := []struct {
		name     string
		wgsLat   float64
		wgsLng   float64
		tolerance float64
	}{
		{
			name:      "Beijing",
			wgsLat:    39.9042,
			wgsLng:    116.4074,
			tolerance: 0.01, // 10m tolerance
		},
		{
			name:      "Shanghai",
			wgsLat:    31.2304,
			wgsLng:    121.4737,
			tolerance: 0.01,
		},
		{
			name:      "Guangzhou",
			wgsLat:    23.1291,
			wgsLng:    113.2644,
			tolerance: 0.01,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Skip if outside China
			if isOutOFChina(tt.wgsLat, tt.wgsLng) {
				t.Skip("Coordinate is outside China")
			}
			
			// WGS -> GCJ -> WGS
			gcjLat, gcjLng := WGStoGCJ(tt.wgsLat, tt.wgsLng)
			backWgsLat, backWgsLng := GCJtoWGS(gcjLat, gcjLng)
			
			// Check round-trip accuracy
			latDiff := math.Abs(backWgsLat - tt.wgsLat)
			lngDiff := math.Abs(backWgsLng - tt.wgsLng)
			
			if latDiff > tt.tolerance {
				t.Errorf("Round-trip lat difference too large: %v > %v", latDiff, tt.tolerance)
			}
			if lngDiff > tt.tolerance {
				t.Errorf("Round-trip lng difference too large: %v > %v", lngDiff, tt.tolerance)
			}
		})
	}
}
