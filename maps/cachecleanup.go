package maps

import (
	"bfmap/config"
	"bfmap/db"
	"bfmap/dbproto"
	"errors"
	"log/slog"
	"math"
	"path/filepath"
	"slices"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/ygrpc/protodb/crud"
)

func CheckAndDeleteMapCacheLoop() {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		slog.Error("create fsnotify watcher for map cache db fail", "error", err)
		return
	}

	err = watcher.Add(filepath.Join(config.DbDir, "bbolt"))
	if err != nil {
		slog.Error("add fsnotify watcher for map cache db fail", "error", err)
		return
	}

	needFreeSize := int64(math.Min(100*1024*1024, float64(config.ImageCacheSize)/10))

	isProcessing := false
	for {
		select {
		case ev := <-watcher.Events:
			if ev.Op.Has(fsnotify.Write) && !isProcessing {
				isProcessing = true
				go func() {
					checkAndDeleteMapCache(needFreeSize)
					isProcessing = false
				}()
			}
		case err := <-watcher.Errors:
			if config.IsVerboseDebugMap {
				slog.Warn("watch map cache db catch error", "error", err)
			}
		}
	}
}

func checkAndDeleteMapCache(needFreeSize int64) {
	size := db.KeyValueDbDiskSize()
	if size < config.ImageCacheSize {
		return
	}

	// need free at least 100MB or 10% of imageCacheSize
	needDeleteSize := size - config.ImageCacheSize + needFreeSize

	retryCount := 0
	for db.KeyValueDbFreeSize() < needDeleteSize {
		slog.Debug("start to delete map cache")
		cacheIndexs1, err1 := queryCanDeleteMapCacheIndex(MapTypeRoadmap)
		cacheIndexs2, err2 := queryCanDeleteMapCacheIndex(MapTypeSatellite)
		cacheIndexs3, err3 := queryCanDeleteMapCacheIndex(MapTypeHybrid)

		if err1 != nil || err2 != nil || err3 != nil {
			if config.IsVerboseDebugMap {
				slog.Warn("query can delete map cache index fail", "error1", err1, "error2", err2, "error3", err3)
			}

			time.Sleep(1 * time.Second)
			retryCount++
			if retryCount > 10 {
				slog.Warn("retry to delete map cache fail", "retry count", retryCount)
				return
			}
			if config.IsVerboseDebugMap {
				slog.Warn("retry to delete map cache", "retry count", retryCount)
			}
			continue
		}

		cacheIndexs := append(cacheIndexs1, cacheIndexs2...)
		cacheIndexs = append(cacheIndexs, cacheIndexs3...)

		// sort by status and access time, if status is the same, sort by access time asc, if status is not the same, sort by status desc
		slices.SortFunc[[]MapTileCacheIndex, MapTileCacheIndex](cacheIndexs, func(a, b MapTileCacheIndex) int {
			if a.GetStatus() == b.GetStatus() {
				return int(a.GetAccessTime() - b.GetAccessTime())
			}
			return int(b.GetStatus() - a.GetStatus())
		})

		for _, index := range cacheIndexs {
			err := deleteMapTileCache(index)
			if err != nil {
				slog.Warn("checkAndDeleteMapCache delete map tile cache fail", "index", index, "error", err)
				continue
			}

			if db.KeyValueDbFreeSize() >= needDeleteSize {
				break
			}
		}
	}
}

func queryCanDeleteMapCacheIndex(mapType string) ([]MapTileCacheIndex, error) {
	var tableName string
	switch mapType {
	case MapTypeRoadmap:
		tableName = "DbMapCacheRoadmapIndex"
	case MapTypeSatellite:
		tableName = "DbMapCacheSatelliteIndex"
	case MapTypeHybrid:
		tableName = "DbMapCacheHybridIndex"
	default:
		return nil, errors.New("unknown map type")
	}

	sqlStr := `SELECT * FROM ` + tableName + `  ORDER BY CASE WHEN Status = 8 THEN 1 ELSE 2 END, AccessTime LIMIT 100`
	dbConn, err := db.GetDbConn()
	if err != nil {
		return nil, err
	}

	rows, err := dbConn.ReadDB.Query(sqlStr)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	result := make([]MapTileCacheIndex, 0, 100)
	for rows.Next() {
		switch mapType {
		case MapTypeRoadmap:
			index := &dbproto.DbMapCacheRoadmapIndex{}
			err := crud.DbScan2ProtoMsg(rows, index, nil, nil)
			if err != nil {
				return nil, err
			}
			result = append(result, index)
		case MapTypeSatellite:
			index := &dbproto.DbMapCacheSatelliteIndex{}
			err := crud.DbScan2ProtoMsg(rows, index, nil, nil)
			if err != nil {
				return nil, err
			}
			result = append(result, index)
		case MapTypeHybrid:
			index := &dbproto.DbMapCacheHybridIndex{}
			err := crud.DbScan2ProtoMsg(rows, index, nil, nil)
			if err != nil {
				return nil, err
			}
			result = append(result, index)
		default:
			return nil, errors.New("unknown map type")
		}
	}

	return result, nil
}

func deleteMapTileCache(index MapTileCacheIndex) error {
	mapReq := IndexToMapReq(index)
	if mapReq == nil {
		return errors.New("index to map req fail")
	}

	err := DeleteTileFromKeyValueDb(mapReq.Key(), index.GetTileHash())
	if err != nil {
		return err
	}

	dbConn, err := db.GetDbConn()
	if err != nil {
		return err
	}

	_, err = crud.DbDelete(dbConn.WriteDB, index, "")
	return err
}

func IndexToMapReq(index MapTileCacheIndex) *MapReq {
	switch i := index.(type) {
	case *dbproto.DbMapCacheRoadmapIndex:
		return &MapReq{
			MapType:        MapTypeRoadmap,
			X:              int(i.TileX),
			Y:              int(i.TileY),
			Z:              int(i.TileZ),
			Lang:           i.Language,
			Provider:       int(i.Provider),
			ImageFormatInt: int(i.TileImageFormat),
			EnableGcj02:    int(i.Gcj02),
		}
	case *dbproto.DbMapCacheSatelliteIndex:
		return &MapReq{
			MapType:        MapTypeSatellite,
			X:              int(i.TileX),
			Y:              int(i.TileY),
			Z:              int(i.TileZ),
			Provider:       int(i.Provider),
			ImageFormatInt: int(i.TileImageFormat),
			EnableGcj02:    int(i.Gcj02),
		}
	case *dbproto.DbMapCacheHybridIndex:
		return &MapReq{
			MapType:        MapTypeHybrid,
			X:              int(i.TileX),
			Y:              int(i.TileY),
			Z:              int(i.TileZ),
			Provider:       int(i.Provider),
			ImageFormatInt: int(i.TileImageFormat),
			EnableGcj02:    int(i.Gcj02),
		}
	default:
		return nil
	}
}
